package errors

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"runtime"
	"time"

	kerrors "github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
)

// 🚀 Enhanced HVAC Error Handling for Kratos Framework Excellence
// Enterprise-grade error handling with context, tracing, and recovery

// ==========================================
// HVAC ERROR TYPES
// ==========================================

// HVACError represents a comprehensive HVAC system error
type HVACError struct {
	BaseError   *kerrors.Error         `json:"base_error"`
	Context     *ErrorContext          `json:"context"`
	Trace       *ErrorTrace            `json:"trace"`
	Suggestions []string               `json:"suggestions"`
	Metadata    map[string]interface{} `json:"metadata"`
	Timestamp   time.Time              `json:"timestamp"`
}

// Error implements the error interface
func (e *HVACError) Error() string {
	if e.BaseError != nil {
		return e.BaseError.Error()
	}
	return "unknown HVAC error"
}

// ErrorContext provides contextual information about the error
type ErrorContext struct {
	ServiceType    string                 `json:"service_type"`
	Operation      string                 `json:"operation"`
	UserID         string                 `json:"user_id,omitempty"`
	CustomerID     string                 `json:"customer_id,omitempty"`
	JobID          string                 `json:"job_id,omitempty"`
	TechnicianID   string                 `json:"technician_id,omitempty"`
	RequestID      string                 `json:"request_id"`
	SessionID      string                 `json:"session_id,omitempty"`
	IPAddress      string                 `json:"ip_address,omitempty"`
	UserAgent      string                 `json:"user_agent,omitempty"`
	Environment    string                 `json:"environment"`
	Version        string                 `json:"version"`
	AdditionalData map[string]interface{} `json:"additional_data,omitempty"`
}

// ErrorTrace provides stack trace and execution path information
type ErrorTrace struct {
	StackTrace    []StackFrame  `json:"stack_trace"`
	ExecutionPath []string      `json:"execution_path"`
	Duration      time.Duration `json:"duration"`
	MemoryUsage   uint64        `json:"memory_usage"`
}

// StackFrame represents a single frame in the stack trace
type StackFrame struct {
	Function string `json:"function"`
	File     string `json:"file"`
	Line     int    `json:"line"`
}

// ==========================================
// HVAC ERROR CODES
// ==========================================

// HVAC-specific error codes
const (
	// Customer errors (1000-1999)
	ErrorCodeCustomerNotFound     = "HVAC_CUSTOMER_NOT_FOUND"
	ErrorCodeCustomerInvalid      = "HVAC_CUSTOMER_INVALID"
	ErrorCodeCustomerDuplicate    = "HVAC_CUSTOMER_DUPLICATE"
	ErrorCodeCustomerUnauthorized = "HVAC_CUSTOMER_UNAUTHORIZED"

	// Technician errors (2000-2999)
	ErrorCodeTechnicianNotFound    = "HVAC_TECHNICIAN_NOT_FOUND"
	ErrorCodeTechnicianUnavailable = "HVAC_TECHNICIAN_UNAVAILABLE"
	ErrorCodeTechnicianUnqualified = "HVAC_TECHNICIAN_UNQUALIFIED"
	ErrorCodeTechnicianOverbooked  = "HVAC_TECHNICIAN_OVERBOOKED"

	// Job errors (3000-3999)
	ErrorCodeJobNotFound           = "HVAC_JOB_NOT_FOUND"
	ErrorCodeJobInvalidStatus      = "HVAC_JOB_INVALID_STATUS"
	ErrorCodeJobScheduleConflict   = "HVAC_JOB_SCHEDULE_CONFLICT"
	ErrorCodeJobRequirementsNotMet = "HVAC_JOB_REQUIREMENTS_NOT_MET"

	// Equipment errors (4000-4999)
	ErrorCodeEquipmentNotFound     = "HVAC_EQUIPMENT_NOT_FOUND"
	ErrorCodeEquipmentMalfunction  = "HVAC_EQUIPMENT_MALFUNCTION"
	ErrorCodeEquipmentOutdated     = "HVAC_EQUIPMENT_OUTDATED"
	ErrorCodeEquipmentIncompatible = "HVAC_EQUIPMENT_INCOMPATIBLE"

	// AI/Analytics errors (5000-5999)
	ErrorCodeAIModelNotAvailable       = "HVAC_AI_MODEL_NOT_AVAILABLE"
	ErrorCodeAIProcessingFailed        = "HVAC_AI_PROCESSING_FAILED"
	ErrorCodeAnalyticsDataInsufficient = "HVAC_ANALYTICS_DATA_INSUFFICIENT"
	ErrorCodePredictionFailed          = "HVAC_PREDICTION_FAILED"

	// System errors (6000-6999)
	ErrorCodeSystemOverload      = "HVAC_SYSTEM_OVERLOAD"
	ErrorCodeDatabaseConnection  = "HVAC_DATABASE_CONNECTION"
	ErrorCodeExternalServiceDown = "HVAC_EXTERNAL_SERVICE_DOWN"
	ErrorCodeConfigurationError  = "HVAC_CONFIGURATION_ERROR"

	// Business logic errors (7000-7999)
	ErrorCodeBusinessRuleViolation = "HVAC_BUSINESS_RULE_VIOLATION"
	ErrorCodeWorkflowError         = "HVAC_WORKFLOW_ERROR"
	ErrorCodeValidationFailed      = "HVAC_VALIDATION_FAILED"
	ErrorCodePermissionDenied      = "HVAC_PERMISSION_DENIED"
)

// ==========================================
// ERROR FACTORY
// ==========================================

// HVACErrorFactory creates HVAC-specific errors with context
type HVACErrorFactory struct {
	log         *log.Helper
	environment string
	version     string
}

// NewHVACErrorFactory creates a new HVAC error factory
func NewHVACErrorFactory(logger log.Logger, environment, version string) *HVACErrorFactory {
	return &HVACErrorFactory{
		log:         log.NewHelper(logger),
		environment: environment,
		version:     version,
	}
}

// NewCustomerError creates a customer-related error
func (f *HVACErrorFactory) NewCustomerError(ctx context.Context, code string, message string, customerID string) *HVACError {
	return f.newHVACError(ctx, code, message, map[string]interface{}{
		"customer_id": customerID,
		"domain":      "customer",
	})
}

// NewTechnicianError creates a technician-related error
func (f *HVACErrorFactory) NewTechnicianError(ctx context.Context, code string, message string, technicianID string) *HVACError {
	return f.newHVACError(ctx, code, message, map[string]interface{}{
		"technician_id": technicianID,
		"domain":        "technician",
	})
}

// NewJobError creates a job-related error
func (f *HVACErrorFactory) NewJobError(ctx context.Context, code string, message string, jobID string) *HVACError {
	return f.newHVACError(ctx, code, message, map[string]interface{}{
		"job_id": jobID,
		"domain": "job",
	})
}

// NewSystemError creates a system-related error
func (f *HVACErrorFactory) NewSystemError(ctx context.Context, code string, message string, component string) *HVACError {
	return f.newHVACError(ctx, code, message, map[string]interface{}{
		"component": component,
		"domain":    "system",
	})
}

// NewBusinessError creates a business logic error
func (f *HVACErrorFactory) NewBusinessError(ctx context.Context, code string, message string, rule string) *HVACError {
	return f.newHVACError(ctx, code, message, map[string]interface{}{
		"business_rule": rule,
		"domain":        "business",
	})
}

// newHVACError creates a new HVAC error with full context
func (f *HVACErrorFactory) newHVACError(ctx context.Context, code string, message string, metadata map[string]interface{}) *HVACError {
	// Create base Kratos error
	baseError := kerrors.New(500, code, message)

	// Extract context information
	errorContext := f.extractErrorContext(ctx)

	// Generate stack trace
	trace := f.generateErrorTrace()

	// Generate suggestions based on error code
	suggestions := f.generateSuggestions(code)

	hvacError := &HVACError{
		BaseError:   baseError,
		Context:     errorContext,
		Trace:       trace,
		Suggestions: suggestions,
		Metadata:    metadata,
		Timestamp:   time.Now(),
	}

	// Log the error
	f.logError(hvacError)

	return hvacError
}

// ==========================================
// ERROR CONTEXT EXTRACTION
// ==========================================

func (f *HVACErrorFactory) extractErrorContext(ctx context.Context) *ErrorContext {
	errorContext := &ErrorContext{
		Environment:    f.environment,
		Version:        f.version,
		AdditionalData: make(map[string]interface{}),
	}

	// Extract request ID
	if requestID := ctx.Value("request_id"); requestID != nil {
		if id, ok := requestID.(string); ok {
			errorContext.RequestID = id
		}
	}

	// Extract user information
	if user := ctx.Value("hvac_user"); user != nil {
		if userData, ok := user.(map[string]interface{}); ok {
			if userID, exists := userData["id"]; exists {
				errorContext.UserID = fmt.Sprintf("%v", userID)
			}
		}
	}

	// Extract service type from context
	if serviceType := ctx.Value("service_type"); serviceType != nil {
		if st, ok := serviceType.(string); ok {
			errorContext.ServiceType = st
		}
	}

	// Extract operation from context
	if operation := ctx.Value("operation"); operation != nil {
		if op, ok := operation.(string); ok {
			errorContext.Operation = op
		}
	}

	return errorContext
}

// ==========================================
// STACK TRACE GENERATION
// ==========================================

func (f *HVACErrorFactory) generateErrorTrace() *ErrorTrace {
	// Get stack trace
	stackFrames := make([]StackFrame, 0)

	// Skip the first few frames (this function, error creation, etc.)
	for i := 3; i < 10; i++ {
		pc, file, line, ok := runtime.Caller(i)
		if !ok {
			break
		}

		fn := runtime.FuncForPC(pc)
		if fn == nil {
			continue
		}

		stackFrames = append(stackFrames, StackFrame{
			Function: fn.Name(),
			File:     file,
			Line:     line,
		})
	}

	// Get memory usage
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	return &ErrorTrace{
		StackTrace:  stackFrames,
		MemoryUsage: m.Alloc,
	}
}

// ==========================================
// ERROR SUGGESTIONS
// ==========================================

func (f *HVACErrorFactory) generateSuggestions(code string) []string {
	suggestions := make([]string, 0)

	switch code {
	case ErrorCodeCustomerNotFound:
		suggestions = append(suggestions,
			"Verify the customer ID is correct",
			"Check if the customer was recently deleted",
			"Ensure you have permission to access this customer",
		)
	case ErrorCodeTechnicianUnavailable:
		suggestions = append(suggestions,
			"Check technician schedule for availability",
			"Consider assigning to a different technician",
			"Reschedule the job for a later time",
		)
	case ErrorCodeJobScheduleConflict:
		suggestions = append(suggestions,
			"Check for overlapping appointments",
			"Verify technician availability",
			"Consider adjusting the job duration",
		)
	case ErrorCodeSystemOverload:
		suggestions = append(suggestions,
			"Retry the request after a short delay",
			"Check system status dashboard",
			"Contact system administrator if issue persists",
		)
	case ErrorCodeAIProcessingFailed:
		suggestions = append(suggestions,
			"Verify input data format and quality",
			"Check if AI service is available",
			"Try with a smaller dataset",
		)
	default:
		suggestions = append(suggestions,
			"Check the error details for more information",
			"Verify your input parameters",
			"Contact support if the issue persists",
		)
	}

	return suggestions
}

// ==========================================
// ERROR LOGGING
// ==========================================

func (f *HVACErrorFactory) logError(hvacError *HVACError) {
	// Create structured log entry
	logData := map[string]interface{}{
		"error_code":    hvacError.BaseError.Reason,
		"error_message": hvacError.BaseError.Message,
		"context":       hvacError.Context,
		"metadata":      hvacError.Metadata,
		"timestamp":     hvacError.Timestamp,
	}

	// Add stack trace for severe errors
	if hvacError.BaseError.Code >= 500 {
		logData["stack_trace"] = hvacError.Trace.StackTrace
	}

	// Log with appropriate level
	switch {
	case hvacError.BaseError.Code >= 500:
		f.log.Errorw("HVAC System Error", logData)
	case hvacError.BaseError.Code >= 400:
		f.log.Warnw("HVAC Client Error", logData)
	default:
		f.log.Infow("HVAC Info", logData)
	}
}

// ==========================================
// ERROR RECOVERY
// ==========================================

// HVACErrorRecovery provides error recovery mechanisms
type HVACErrorRecovery struct {
	log            *log.Helper
	retryConfig    *RetryConfig
	circuitBreaker *CircuitBreaker
}

// RetryConfig configures retry behavior
type RetryConfig struct {
	MaxRetries      int           `json:"max_retries"`
	InitialDelay    time.Duration `json:"initial_delay"`
	MaxDelay        time.Duration `json:"max_delay"`
	BackoffFactor   float64       `json:"backoff_factor"`
	RetryableErrors []string      `json:"retryable_errors"`
}

// CircuitBreaker prevents cascading failures
type CircuitBreaker struct {
	FailureThreshold int           `json:"failure_threshold"`
	RecoveryTimeout  time.Duration `json:"recovery_timeout"`
	State            string        `json:"state"` // closed, open, half-open
}

// NewHVACErrorRecovery creates a new error recovery system
func NewHVACErrorRecovery(logger log.Logger) *HVACErrorRecovery {
	return &HVACErrorRecovery{
		log: log.NewHelper(logger),
		retryConfig: &RetryConfig{
			MaxRetries:    3,
			InitialDelay:  100 * time.Millisecond,
			MaxDelay:      5 * time.Second,
			BackoffFactor: 2.0,
			RetryableErrors: []string{
				ErrorCodeSystemOverload,
				ErrorCodeDatabaseConnection,
				ErrorCodeExternalServiceDown,
			},
		},
		circuitBreaker: &CircuitBreaker{
			FailureThreshold: 5,
			RecoveryTimeout:  30 * time.Second,
			State:            "closed",
		},
	}
}

// ShouldRetry determines if an error should be retried
func (r *HVACErrorRecovery) ShouldRetry(hvacError *HVACError, attemptCount int) bool {
	// Check max retries
	if attemptCount >= r.retryConfig.MaxRetries {
		return false
	}

	// Check if error is retryable
	for _, retryableCode := range r.retryConfig.RetryableErrors {
		if hvacError.BaseError.Reason == retryableCode {
			return true
		}
	}

	return false
}

// CalculateDelay calculates the delay before retry
func (r *HVACErrorRecovery) CalculateDelay(attemptCount int) time.Duration {
	delay := time.Duration(float64(r.retryConfig.InitialDelay) *
		(r.retryConfig.BackoffFactor * float64(attemptCount)))

	if delay > r.retryConfig.MaxDelay {
		delay = r.retryConfig.MaxDelay
	}

	return delay
}

// ==========================================
// ERROR UTILITIES
// ==========================================

// IsHVACError checks if an error is an HVAC error
func IsHVACError(err error) bool {
	var hvacErr *HVACError
	return errors.As(err, &hvacErr)
}

// ExtractHVACError extracts HVAC error from any error
func ExtractHVACError(err error) *HVACError {
	var hvacErr *HVACError
	if errors.As(err, &hvacErr) {
		return hvacErr
	}
	return nil
}

// WrapError wraps a standard error as an HVAC error
func WrapError(ctx context.Context, err error, factory *HVACErrorFactory) *HVACError {
	if hvacErr := ExtractHVACError(err); hvacErr != nil {
		return hvacErr
	}

	return factory.newHVACError(ctx, "HVAC_WRAPPED_ERROR", err.Error(), map[string]interface{}{
		"original_error": err.Error(),
		"wrapped":        true,
	})
}

// ErrorToJSON converts an HVAC error to JSON
func ErrorToJSON(hvacError *HVACError) ([]byte, error) {
	return json.Marshal(hvacError)
}

// ErrorFromJSON creates an HVAC error from JSON
func ErrorFromJSON(data []byte) (*HVACError, error) {
	var hvacError HVACError
	err := json.Unmarshal(data, &hvacError)
	if err != nil {
		return nil, err
	}
	return &hvacError, nil
}
