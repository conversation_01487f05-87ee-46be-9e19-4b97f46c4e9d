# 🌐 A2A Protocol Examples - HVAC CRM Agent

## Quick Start Examples

### 1. Agent <PERSON>

```bash
# Get public agent card
curl -s http://localhost:8083/.well-known/agent.json | jq .

# Response includes:
# - Agent name and description
# - Available skills
# - Capabilities (streaming, push notifications)
# - Security schemes
```

### 2. HVAC Diagnostics

```bash
# Diagnose HVAC system issues
curl -X POST http://localhost:8083/a2a/message/send \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": "diag-001",
    "method": "message/send",
    "params": {
      "message": {
        "role": "user",
        "parts": [
          {
            "type": "text",
            "text": "My air conditioning is making loud noises and not cooling properly. The system is 5 years old."
          }
        ]
      }
    }
  }'
```

### 3. Customer Service Inquiry

```bash
# Handle customer service request
curl -X POST http://localhost:8083/a2a/message/send \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": "service-001",
    "method": "message/send",
    "params": {
      "message": {
        "role": "user",
        "parts": [
          {
            "type": "text",
            "text": "I need to schedule an emergency HVAC repair for my restaurant. The heating system stopped working."
          }
        ]
      }
    }
  }'
```

### 4. Equipment Status Check

```bash
# Check equipment status
curl -X POST http://localhost:8083/a2a/message/send \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": "status-001",
    "method": "message/send",
    "params": {
      "message": {
        "role": "user",
        "parts": [
          {
            "type": "text",
            "text": "Check the status of equipment ID HVAC-001 for customer ABC Corp"
          }
        ]
      }
    }
  }'
```

### 5. Maintenance Planning

```bash
# Generate maintenance plan
curl -X POST http://localhost:8083/a2a/message/send \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": "maint-001",
    "method": "message/send",
    "params": {
      "message": {
        "role": "user",
        "parts": [
          {
            "type": "text",
            "text": "Create a 6-month maintenance plan for our office building HVAC systems"
          }
        ]
      }
    }
  }'
```

### 6. Quote Generation

```bash
# Generate service quote
curl -X POST http://localhost:8083/a2a/message/send \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": "quote-001",
    "method": "message/send",
    "params": {
      "message": {
        "role": "user",
        "parts": [
          {
            "type": "text",
            "text": "I need a quote for installing a new heat pump system in a 2000 sq ft home in Warsaw"
          }
        ]
      }
    }
  }'
```

## Advanced Examples

### 7. Streaming Message with SSE

```bash
# Send streaming message
curl -X POST http://localhost:8083/a2a/message/stream \
  -H "Content-Type: application/json" \
  -H "Accept: text/event-stream" \
  -d '{
    "jsonrpc": "2.0",
    "id": "stream-001",
    "method": "message/stream",
    "params": {
      "message": {
        "role": "user",
        "parts": [
          {
            "type": "text",
            "text": "Perform comprehensive HVAC system analysis with real-time updates"
          }
        ]
      }
    }
  }'
```

### 8. Task Management

```bash
# Get task status
curl -X POST http://localhost:8083/a2a/tasks/get \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": "get-task-001",
    "method": "tasks/get",
    "params": {
      "id": "task-12345"
    }
  }'

# Cancel task
curl -X POST http://localhost:8083/a2a/tasks/cancel \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": "cancel-task-001",
    "method": "tasks/cancel",
    "params": {
      "id": "task-12345"
    }
  }'
```

### 9. Multi-Part Message with Data

```bash
# Send message with structured data
curl -X POST http://localhost:8083/a2a/message/send \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": "data-001",
    "method": "message/send",
    "params": {
      "message": {
        "role": "user",
        "parts": [
          {
            "type": "text",
            "text": "Analyze this HVAC system data"
          },
          {
            "type": "data",
            "data": {
              "system_type": "heat_pump",
              "temperature_readings": [18.5, 19.2, 20.1],
              "pressure_readings": [2.1, 2.3, 2.2],
              "error_codes": ["E001", "W003"]
            }
          }
        ]
      }
    }
  }'
```

### 10. Authenticated Extended Card

```bash
# Get extended capabilities (requires authentication)
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  http://localhost:8083/a2a/agent/authenticatedExtendedCard
```

## Integration Examples

### 11. Python Client Example

```python
import requests
import json

class HVACAIAgent:
    def __init__(self, base_url="http://localhost:8083"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def send_message(self, text, message_id=None):
        if not message_id:
            message_id = f"py-{int(time.time())}"
        
        payload = {
            "jsonrpc": "2.0",
            "id": message_id,
            "method": "message/send",
            "params": {
                "message": {
                    "role": "user",
                    "parts": [{"type": "text", "text": text}]
                }
            }
        }
        
        response = self.session.post(
            f"{self.base_url}/a2a/message/send",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        
        return response.json()
    
    def get_agent_info(self):
        response = self.session.get(f"{self.base_url}/.well-known/agent.json")
        return response.json()

# Usage
agent = HVACAIAgent()
info = agent.get_agent_info()
print(f"Agent: {info['name']}")
print(f"Skills: {[skill['name'] for skill in info['skills']]}")

result = agent.send_message("Check HVAC system performance")
print(f"Response: {result}")
```

### 12. JavaScript/Node.js Client

```javascript
class HVACAIAgent {
    constructor(baseUrl = 'http://localhost:8083') {
        this.baseUrl = baseUrl;
    }
    
    async sendMessage(text, messageId = null) {
        if (!messageId) {
            messageId = `js-${Date.now()}`;
        }
        
        const payload = {
            jsonrpc: '2.0',
            id: messageId,
            method: 'message/send',
            params: {
                message: {
                    role: 'user',
                    parts: [{ type: 'text', text }]
                }
            }
        };
        
        const response = await fetch(`${this.baseUrl}/a2a/message/send`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(payload)
        });
        
        return await response.json();
    }
    
    async getAgentInfo() {
        const response = await fetch(`${this.baseUrl}/.well-known/agent.json`);
        return await response.json();
    }
}

// Usage
const agent = new HVACAIAgent();

agent.getAgentInfo().then(info => {
    console.log(`Agent: ${info.name}`);
    console.log(`Skills: ${info.skills.map(s => s.name).join(', ')}`);
});

agent.sendMessage('Schedule HVAC maintenance').then(result => {
    console.log('Response:', result);
});
```

## Error Handling Examples

### 13. Common Error Responses

```json
// Invalid method
{
  "jsonrpc": "2.0",
  "id": "test",
  "error": {
    "code": -32601,
    "message": "Method not found"
  }
}

// Invalid parameters
{
  "jsonrpc": "2.0",
  "id": "test",
  "error": {
    "code": -32602,
    "message": "Invalid params",
    "data": "missing required field: message"
  }
}

// Task not found
{
  "jsonrpc": "2.0",
  "id": "test",
  "error": {
    "code": -32001,
    "message": "Task not found"
  }
}
```

## Performance Testing

### 14. Load Testing Script

```bash
#!/bin/bash
# Simple load test for A2A endpoints

echo "Testing agent discovery..."
ab -n 100 -c 10 http://localhost:8083/.well-known/agent.json

echo "Testing message sending..."
ab -n 50 -c 5 -p test_message.json -T application/json \
  http://localhost:8083/a2a/message/send
```

### 15. Monitoring Commands

```bash
# Check server health
curl http://localhost:8083/health

# Monitor logs
tail -f /var/log/hvac-crm/a2a.log

# Check metrics
curl http://localhost:8083/metrics
```

---

These examples demonstrate the full capabilities of our A2A implementation, from basic agent discovery to advanced multi-agent communication patterns. The HVAC CRM Intelligence Agent is ready to participate in the broader AI agent ecosystem! 🚀
