// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: v1/email.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Message Definitions
type EmailMessage struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	From          string                 `protobuf:"bytes,2,opt,name=from,proto3" json:"from,omitempty"`
	To            []string               `protobuf:"bytes,3,rep,name=to,proto3" json:"to,omitempty"`
	Cc            []string               `protobuf:"bytes,4,rep,name=cc,proto3" json:"cc,omitempty"`
	Bcc           []string               `protobuf:"bytes,5,rep,name=bcc,proto3" json:"bcc,omitempty"`
	Subject       string                 `protobuf:"bytes,6,opt,name=subject,proto3" json:"subject,omitempty"`
	Body          string                 `protobuf:"bytes,7,opt,name=body,proto3" json:"body,omitempty"`
	HtmlBody      string                 `protobuf:"bytes,8,opt,name=html_body,json=htmlBody,proto3" json:"html_body,omitempty"`
	Attachments   []*Attachment          `protobuf:"bytes,9,rep,name=attachments,proto3" json:"attachments,omitempty"`
	Headers       map[string]string      `protobuf:"bytes,10,rep,name=headers,proto3" json:"headers,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Priority      string                 `protobuf:"bytes,11,opt,name=priority,proto3" json:"priority,omitempty"`
	Timestamp     *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Status        string                 `protobuf:"bytes,13,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EmailMessage) Reset() {
	*x = EmailMessage{}
	mi := &file_v1_email_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EmailMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmailMessage) ProtoMessage() {}

func (x *EmailMessage) ProtoReflect() protoreflect.Message {
	mi := &file_v1_email_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmailMessage.ProtoReflect.Descriptor instead.
func (*EmailMessage) Descriptor() ([]byte, []int) {
	return file_v1_email_proto_rawDescGZIP(), []int{0}
}

func (x *EmailMessage) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *EmailMessage) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *EmailMessage) GetTo() []string {
	if x != nil {
		return x.To
	}
	return nil
}

func (x *EmailMessage) GetCc() []string {
	if x != nil {
		return x.Cc
	}
	return nil
}

func (x *EmailMessage) GetBcc() []string {
	if x != nil {
		return x.Bcc
	}
	return nil
}

func (x *EmailMessage) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

func (x *EmailMessage) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

func (x *EmailMessage) GetHtmlBody() string {
	if x != nil {
		return x.HtmlBody
	}
	return ""
}

func (x *EmailMessage) GetAttachments() []*Attachment {
	if x != nil {
		return x.Attachments
	}
	return nil
}

func (x *EmailMessage) GetHeaders() map[string]string {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *EmailMessage) GetPriority() string {
	if x != nil {
		return x.Priority
	}
	return ""
}

func (x *EmailMessage) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *EmailMessage) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type Attachment struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Filename      string                 `protobuf:"bytes,1,opt,name=filename,proto3" json:"filename,omitempty"`
	ContentType   string                 `protobuf:"bytes,2,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`
	Size          int64                  `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`
	Data          []byte                 `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	Url           string                 `protobuf:"bytes,5,opt,name=url,proto3" json:"url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Attachment) Reset() {
	*x = Attachment{}
	mi := &file_v1_email_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Attachment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Attachment) ProtoMessage() {}

func (x *Attachment) ProtoReflect() protoreflect.Message {
	mi := &file_v1_email_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Attachment.ProtoReflect.Descriptor instead.
func (*Attachment) Descriptor() ([]byte, []int) {
	return file_v1_email_proto_rawDescGZIP(), []int{1}
}

func (x *Attachment) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

func (x *Attachment) GetContentType() string {
	if x != nil {
		return x.ContentType
	}
	return ""
}

func (x *Attachment) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *Attachment) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Attachment) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type Campaign struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Subject       string                 `protobuf:"bytes,3,opt,name=subject,proto3" json:"subject,omitempty"`
	Template      string                 `protobuf:"bytes,4,opt,name=template,proto3" json:"template,omitempty"`
	Recipients    []string               `protobuf:"bytes,5,rep,name=recipients,proto3" json:"recipients,omitempty"`
	ScheduledAt   *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=scheduled_at,json=scheduledAt,proto3" json:"scheduled_at,omitempty"`
	Status        string                 `protobuf:"bytes,7,opt,name=status,proto3" json:"status,omitempty"`
	Stats         *CampaignStats         `protobuf:"bytes,8,opt,name=stats,proto3" json:"stats,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Campaign) Reset() {
	*x = Campaign{}
	mi := &file_v1_email_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Campaign) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Campaign) ProtoMessage() {}

func (x *Campaign) ProtoReflect() protoreflect.Message {
	mi := &file_v1_email_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Campaign.ProtoReflect.Descriptor instead.
func (*Campaign) Descriptor() ([]byte, []int) {
	return file_v1_email_proto_rawDescGZIP(), []int{2}
}

func (x *Campaign) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Campaign) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Campaign) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

func (x *Campaign) GetTemplate() string {
	if x != nil {
		return x.Template
	}
	return ""
}

func (x *Campaign) GetRecipients() []string {
	if x != nil {
		return x.Recipients
	}
	return nil
}

func (x *Campaign) GetScheduledAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ScheduledAt
	}
	return nil
}

func (x *Campaign) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Campaign) GetStats() *CampaignStats {
	if x != nil {
		return x.Stats
	}
	return nil
}

type CampaignStats struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Sent          int32                  `protobuf:"varint,1,opt,name=sent,proto3" json:"sent,omitempty"`
	Delivered     int32                  `protobuf:"varint,2,opt,name=delivered,proto3" json:"delivered,omitempty"`
	Opened        int32                  `protobuf:"varint,3,opt,name=opened,proto3" json:"opened,omitempty"`
	Clicked       int32                  `protobuf:"varint,4,opt,name=clicked,proto3" json:"clicked,omitempty"`
	Bounced       int32                  `protobuf:"varint,5,opt,name=bounced,proto3" json:"bounced,omitempty"`
	OpenRate      float64                `protobuf:"fixed64,6,opt,name=open_rate,json=openRate,proto3" json:"open_rate,omitempty"`
	ClickRate     float64                `protobuf:"fixed64,7,opt,name=click_rate,json=clickRate,proto3" json:"click_rate,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CampaignStats) Reset() {
	*x = CampaignStats{}
	mi := &file_v1_email_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CampaignStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CampaignStats) ProtoMessage() {}

func (x *CampaignStats) ProtoReflect() protoreflect.Message {
	mi := &file_v1_email_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CampaignStats.ProtoReflect.Descriptor instead.
func (*CampaignStats) Descriptor() ([]byte, []int) {
	return file_v1_email_proto_rawDescGZIP(), []int{3}
}

func (x *CampaignStats) GetSent() int32 {
	if x != nil {
		return x.Sent
	}
	return 0
}

func (x *CampaignStats) GetDelivered() int32 {
	if x != nil {
		return x.Delivered
	}
	return 0
}

func (x *CampaignStats) GetOpened() int32 {
	if x != nil {
		return x.Opened
	}
	return 0
}

func (x *CampaignStats) GetClicked() int32 {
	if x != nil {
		return x.Clicked
	}
	return 0
}

func (x *CampaignStats) GetBounced() int32 {
	if x != nil {
		return x.Bounced
	}
	return 0
}

func (x *CampaignStats) GetOpenRate() float64 {
	if x != nil {
		return x.OpenRate
	}
	return 0
}

func (x *CampaignStats) GetClickRate() float64 {
	if x != nil {
		return x.ClickRate
	}
	return 0
}

// Request/Response Messages
type SendEmailRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	From          string                 `protobuf:"bytes,1,opt,name=from,proto3" json:"from,omitempty"`
	To            []string               `protobuf:"bytes,2,rep,name=to,proto3" json:"to,omitempty"`
	Cc            []string               `protobuf:"bytes,3,rep,name=cc,proto3" json:"cc,omitempty"`
	Bcc           []string               `protobuf:"bytes,4,rep,name=bcc,proto3" json:"bcc,omitempty"`
	Subject       string                 `protobuf:"bytes,5,opt,name=subject,proto3" json:"subject,omitempty"`
	Body          string                 `protobuf:"bytes,6,opt,name=body,proto3" json:"body,omitempty"`
	HtmlBody      string                 `protobuf:"bytes,7,opt,name=html_body,json=htmlBody,proto3" json:"html_body,omitempty"`
	Attachments   []*Attachment          `protobuf:"bytes,8,rep,name=attachments,proto3" json:"attachments,omitempty"`
	Priority      string                 `protobuf:"bytes,9,opt,name=priority,proto3" json:"priority,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendEmailRequest) Reset() {
	*x = SendEmailRequest{}
	mi := &file_v1_email_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendEmailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendEmailRequest) ProtoMessage() {}

func (x *SendEmailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_v1_email_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendEmailRequest.ProtoReflect.Descriptor instead.
func (*SendEmailRequest) Descriptor() ([]byte, []int) {
	return file_v1_email_proto_rawDescGZIP(), []int{4}
}

func (x *SendEmailRequest) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *SendEmailRequest) GetTo() []string {
	if x != nil {
		return x.To
	}
	return nil
}

func (x *SendEmailRequest) GetCc() []string {
	if x != nil {
		return x.Cc
	}
	return nil
}

func (x *SendEmailRequest) GetBcc() []string {
	if x != nil {
		return x.Bcc
	}
	return nil
}

func (x *SendEmailRequest) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

func (x *SendEmailRequest) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

func (x *SendEmailRequest) GetHtmlBody() string {
	if x != nil {
		return x.HtmlBody
	}
	return ""
}

func (x *SendEmailRequest) GetAttachments() []*Attachment {
	if x != nil {
		return x.Attachments
	}
	return nil
}

func (x *SendEmailRequest) GetPriority() string {
	if x != nil {
		return x.Priority
	}
	return ""
}

type SendEmailResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	EmailId       string                 `protobuf:"bytes,1,opt,name=email_id,json=emailId,proto3" json:"email_id,omitempty"`
	Status        string                 `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	Message       string                 `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendEmailResponse) Reset() {
	*x = SendEmailResponse{}
	mi := &file_v1_email_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendEmailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendEmailResponse) ProtoMessage() {}

func (x *SendEmailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_v1_email_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendEmailResponse.ProtoReflect.Descriptor instead.
func (*SendEmailResponse) Descriptor() ([]byte, []int) {
	return file_v1_email_proto_rawDescGZIP(), []int{5}
}

func (x *SendEmailResponse) GetEmailId() string {
	if x != nil {
		return x.EmailId
	}
	return ""
}

func (x *SendEmailResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *SendEmailResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type ListEmailsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Limit         int32                  `protobuf:"varint,1,opt,name=limit,proto3" json:"limit,omitempty"`
	Offset        int32                  `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Status        string                 `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListEmailsRequest) Reset() {
	*x = ListEmailsRequest{}
	mi := &file_v1_email_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListEmailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEmailsRequest) ProtoMessage() {}

func (x *ListEmailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_v1_email_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEmailsRequest.ProtoReflect.Descriptor instead.
func (*ListEmailsRequest) Descriptor() ([]byte, []int) {
	return file_v1_email_proto_rawDescGZIP(), []int{6}
}

func (x *ListEmailsRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ListEmailsRequest) GetOffset() int32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *ListEmailsRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type ListEmailsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Emails        []*EmailMessage        `protobuf:"bytes,1,rep,name=emails,proto3" json:"emails,omitempty"`
	Total         int32                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListEmailsResponse) Reset() {
	*x = ListEmailsResponse{}
	mi := &file_v1_email_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListEmailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEmailsResponse) ProtoMessage() {}

func (x *ListEmailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_v1_email_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEmailsResponse.ProtoReflect.Descriptor instead.
func (*ListEmailsResponse) Descriptor() ([]byte, []int) {
	return file_v1_email_proto_rawDescGZIP(), []int{7}
}

func (x *ListEmailsResponse) GetEmails() []*EmailMessage {
	if x != nil {
		return x.Emails
	}
	return nil
}

func (x *ListEmailsResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type CreateCampaignRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Subject       string                 `protobuf:"bytes,2,opt,name=subject,proto3" json:"subject,omitempty"`
	Template      string                 `protobuf:"bytes,3,opt,name=template,proto3" json:"template,omitempty"`
	Recipients    []string               `protobuf:"bytes,4,rep,name=recipients,proto3" json:"recipients,omitempty"`
	ScheduledAt   *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=scheduled_at,json=scheduledAt,proto3" json:"scheduled_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateCampaignRequest) Reset() {
	*x = CreateCampaignRequest{}
	mi := &file_v1_email_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCampaignRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCampaignRequest) ProtoMessage() {}

func (x *CreateCampaignRequest) ProtoReflect() protoreflect.Message {
	mi := &file_v1_email_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCampaignRequest.ProtoReflect.Descriptor instead.
func (*CreateCampaignRequest) Descriptor() ([]byte, []int) {
	return file_v1_email_proto_rawDescGZIP(), []int{8}
}

func (x *CreateCampaignRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateCampaignRequest) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

func (x *CreateCampaignRequest) GetTemplate() string {
	if x != nil {
		return x.Template
	}
	return ""
}

func (x *CreateCampaignRequest) GetRecipients() []string {
	if x != nil {
		return x.Recipients
	}
	return nil
}

func (x *CreateCampaignRequest) GetScheduledAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ScheduledAt
	}
	return nil
}

type CreateCampaignResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Campaign      *Campaign              `protobuf:"bytes,1,opt,name=campaign,proto3" json:"campaign,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateCampaignResponse) Reset() {
	*x = CreateCampaignResponse{}
	mi := &file_v1_email_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCampaignResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCampaignResponse) ProtoMessage() {}

func (x *CreateCampaignResponse) ProtoReflect() protoreflect.Message {
	mi := &file_v1_email_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCampaignResponse.ProtoReflect.Descriptor instead.
func (*CreateCampaignResponse) Descriptor() ([]byte, []int) {
	return file_v1_email_proto_rawDescGZIP(), []int{9}
}

func (x *CreateCampaignResponse) GetCampaign() *Campaign {
	if x != nil {
		return x.Campaign
	}
	return nil
}

type GetCampaignStatsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CampaignId    string                 `protobuf:"bytes,1,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCampaignStatsRequest) Reset() {
	*x = GetCampaignStatsRequest{}
	mi := &file_v1_email_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCampaignStatsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCampaignStatsRequest) ProtoMessage() {}

func (x *GetCampaignStatsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_v1_email_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCampaignStatsRequest.ProtoReflect.Descriptor instead.
func (*GetCampaignStatsRequest) Descriptor() ([]byte, []int) {
	return file_v1_email_proto_rawDescGZIP(), []int{10}
}

func (x *GetCampaignStatsRequest) GetCampaignId() string {
	if x != nil {
		return x.CampaignId
	}
	return ""
}

type GetCampaignStatsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Stats         *CampaignStats         `protobuf:"bytes,1,opt,name=stats,proto3" json:"stats,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCampaignStatsResponse) Reset() {
	*x = GetCampaignStatsResponse{}
	mi := &file_v1_email_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCampaignStatsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCampaignStatsResponse) ProtoMessage() {}

func (x *GetCampaignStatsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_v1_email_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCampaignStatsResponse.ProtoReflect.Descriptor instead.
func (*GetCampaignStatsResponse) Descriptor() ([]byte, []int) {
	return file_v1_email_proto_rawDescGZIP(), []int{11}
}

func (x *GetCampaignStatsResponse) GetStats() *CampaignStats {
	if x != nil {
		return x.Stats
	}
	return nil
}

type AnalyzeSentimentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Content       string                 `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AnalyzeSentimentRequest) Reset() {
	*x = AnalyzeSentimentRequest{}
	mi := &file_v1_email_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AnalyzeSentimentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalyzeSentimentRequest) ProtoMessage() {}

func (x *AnalyzeSentimentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_v1_email_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalyzeSentimentRequest.ProtoReflect.Descriptor instead.
func (*AnalyzeSentimentRequest) Descriptor() ([]byte, []int) {
	return file_v1_email_proto_rawDescGZIP(), []int{12}
}

func (x *AnalyzeSentimentRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type AnalyzeSentimentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Sentiment     string                 `protobuf:"bytes,1,opt,name=sentiment,proto3" json:"sentiment,omitempty"`
	Confidence    float64                `protobuf:"fixed64,2,opt,name=confidence,proto3" json:"confidence,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AnalyzeSentimentResponse) Reset() {
	*x = AnalyzeSentimentResponse{}
	mi := &file_v1_email_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AnalyzeSentimentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalyzeSentimentResponse) ProtoMessage() {}

func (x *AnalyzeSentimentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_v1_email_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalyzeSentimentResponse.ProtoReflect.Descriptor instead.
func (*AnalyzeSentimentResponse) Descriptor() ([]byte, []int) {
	return file_v1_email_proto_rawDescGZIP(), []int{13}
}

func (x *AnalyzeSentimentResponse) GetSentiment() string {
	if x != nil {
		return x.Sentiment
	}
	return ""
}

func (x *AnalyzeSentimentResponse) GetConfidence() float64 {
	if x != nil {
		return x.Confidence
	}
	return 0
}

var File_v1_email_proto protoreflect.FileDescriptor

const file_v1_email_proto_rawDesc = "" +
	"\n" +
	"\x0ev1/email.proto\x12\fapi.email.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xd8\x03\n" +
	"\fEmailMessage\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04from\x18\x02 \x01(\tR\x04from\x12\x0e\n" +
	"\x02to\x18\x03 \x03(\tR\x02to\x12\x0e\n" +
	"\x02cc\x18\x04 \x03(\tR\x02cc\x12\x10\n" +
	"\x03bcc\x18\x05 \x03(\tR\x03bcc\x12\x18\n" +
	"\asubject\x18\x06 \x01(\tR\asubject\x12\x12\n" +
	"\x04body\x18\a \x01(\tR\x04body\x12\x1b\n" +
	"\thtml_body\x18\b \x01(\tR\bhtmlBody\x12:\n" +
	"\vattachments\x18\t \x03(\v2\x18.api.email.v1.AttachmentR\vattachments\x12A\n" +
	"\aheaders\x18\n" +
	" \x03(\v2'.api.email.v1.EmailMessage.HeadersEntryR\aheaders\x12\x1a\n" +
	"\bpriority\x18\v \x01(\tR\bpriority\x128\n" +
	"\ttimestamp\x18\f \x01(\v2\x1a.google.protobuf.TimestampR\ttimestamp\x12\x16\n" +
	"\x06status\x18\r \x01(\tR\x06status\x1a:\n" +
	"\fHeadersEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\x85\x01\n" +
	"\n" +
	"Attachment\x12\x1a\n" +
	"\bfilename\x18\x01 \x01(\tR\bfilename\x12!\n" +
	"\fcontent_type\x18\x02 \x01(\tR\vcontentType\x12\x12\n" +
	"\x04size\x18\x03 \x01(\x03R\x04size\x12\x12\n" +
	"\x04data\x18\x04 \x01(\fR\x04data\x12\x10\n" +
	"\x03url\x18\x05 \x01(\tR\x03url\"\x8e\x02\n" +
	"\bCampaign\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x18\n" +
	"\asubject\x18\x03 \x01(\tR\asubject\x12\x1a\n" +
	"\btemplate\x18\x04 \x01(\tR\btemplate\x12\x1e\n" +
	"\n" +
	"recipients\x18\x05 \x03(\tR\n" +
	"recipients\x12=\n" +
	"\fscheduled_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\vscheduledAt\x12\x16\n" +
	"\x06status\x18\a \x01(\tR\x06status\x121\n" +
	"\x05stats\x18\b \x01(\v2\x1b.api.email.v1.CampaignStatsR\x05stats\"\xc9\x01\n" +
	"\rCampaignStats\x12\x12\n" +
	"\x04sent\x18\x01 \x01(\x05R\x04sent\x12\x1c\n" +
	"\tdelivered\x18\x02 \x01(\x05R\tdelivered\x12\x16\n" +
	"\x06opened\x18\x03 \x01(\x05R\x06opened\x12\x18\n" +
	"\aclicked\x18\x04 \x01(\x05R\aclicked\x12\x18\n" +
	"\abounced\x18\x05 \x01(\x05R\abounced\x12\x1b\n" +
	"\topen_rate\x18\x06 \x01(\x01R\bopenRate\x12\x1d\n" +
	"\n" +
	"click_rate\x18\a \x01(\x01R\tclickRate\"\xfb\x01\n" +
	"\x10SendEmailRequest\x12\x12\n" +
	"\x04from\x18\x01 \x01(\tR\x04from\x12\x0e\n" +
	"\x02to\x18\x02 \x03(\tR\x02to\x12\x0e\n" +
	"\x02cc\x18\x03 \x03(\tR\x02cc\x12\x10\n" +
	"\x03bcc\x18\x04 \x03(\tR\x03bcc\x12\x18\n" +
	"\asubject\x18\x05 \x01(\tR\asubject\x12\x12\n" +
	"\x04body\x18\x06 \x01(\tR\x04body\x12\x1b\n" +
	"\thtml_body\x18\a \x01(\tR\bhtmlBody\x12:\n" +
	"\vattachments\x18\b \x03(\v2\x18.api.email.v1.AttachmentR\vattachments\x12\x1a\n" +
	"\bpriority\x18\t \x01(\tR\bpriority\"`\n" +
	"\x11SendEmailResponse\x12\x19\n" +
	"\bemail_id\x18\x01 \x01(\tR\aemailId\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\x12\x18\n" +
	"\amessage\x18\x03 \x01(\tR\amessage\"Y\n" +
	"\x11ListEmailsRequest\x12\x14\n" +
	"\x05limit\x18\x01 \x01(\x05R\x05limit\x12\x16\n" +
	"\x06offset\x18\x02 \x01(\x05R\x06offset\x12\x16\n" +
	"\x06status\x18\x03 \x01(\tR\x06status\"^\n" +
	"\x12ListEmailsResponse\x122\n" +
	"\x06emails\x18\x01 \x03(\v2\x1a.api.email.v1.EmailMessageR\x06emails\x12\x14\n" +
	"\x05total\x18\x02 \x01(\x05R\x05total\"\xc0\x01\n" +
	"\x15CreateCampaignRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x18\n" +
	"\asubject\x18\x02 \x01(\tR\asubject\x12\x1a\n" +
	"\btemplate\x18\x03 \x01(\tR\btemplate\x12\x1e\n" +
	"\n" +
	"recipients\x18\x04 \x03(\tR\n" +
	"recipients\x12=\n" +
	"\fscheduled_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\vscheduledAt\"L\n" +
	"\x16CreateCampaignResponse\x122\n" +
	"\bcampaign\x18\x01 \x01(\v2\x16.api.email.v1.CampaignR\bcampaign\":\n" +
	"\x17GetCampaignStatsRequest\x12\x1f\n" +
	"\vcampaign_id\x18\x01 \x01(\tR\n" +
	"campaignId\"M\n" +
	"\x18GetCampaignStatsResponse\x121\n" +
	"\x05stats\x18\x01 \x01(\v2\x1b.api.email.v1.CampaignStatsR\x05stats\"3\n" +
	"\x17AnalyzeSentimentRequest\x12\x18\n" +
	"\acontent\x18\x01 \x01(\tR\acontent\"X\n" +
	"\x18AnalyzeSentimentResponse\x12\x1c\n" +
	"\tsentiment\x18\x01 \x01(\tR\tsentiment\x12\x1e\n" +
	"\n" +
	"confidence\x18\x02 \x01(\x01R\n" +
	"confidence2\x84\x05\n" +
	"\fEmailService\x12l\n" +
	"\tSendEmail\x12\x1e.api.email.v1.SendEmailRequest\x1a\x1f.api.email.v1.SendEmailResponse\"\x1e\x82\xd3\xe4\x93\x02\x18:\x01*\"\x13/api/v1/emails/send\x12g\n" +
	"\n" +
	"ListEmails\x12\x1f.api.email.v1.ListEmailsRequest\x1a .api.email.v1.ListEmailsResponse\"\x16\x82\xd3\xe4\x93\x02\x10\x12\x0e/api/v1/emails\x12y\n" +
	"\x0eCreateCampaign\x12#.api.email.v1.CreateCampaignRequest\x1a$.api.email.v1.CreateCampaignResponse\"\x1c\x82\xd3\xe4\x93\x02\x16:\x01*\"\x11/api/v1/campaigns\x12\x90\x01\n" +
	"\x10GetCampaignStats\x12%.api.email.v1.GetCampaignStatsRequest\x1a&.api.email.v1.GetCampaignStatsResponse\"-\x82\xd3\xe4\x93\x02'\x12%/api/v1/campaigns/{campaign_id}/stats\x12\x8e\x01\n" +
	"\x10AnalyzeSentiment\x12%.api.email.v1.AnalyzeSentimentRequest\x1a&.api.email.v1.AnalyzeSentimentResponse\"+\x82\xd3\xe4\x93\x02%:\x01*\" /api/v1/emails/analyze-sentimentB'Z%gobackend-hvac-kratos/api/email/v1;v1b\x06proto3"

var (
	file_v1_email_proto_rawDescOnce sync.Once
	file_v1_email_proto_rawDescData []byte
)

func file_v1_email_proto_rawDescGZIP() []byte {
	file_v1_email_proto_rawDescOnce.Do(func() {
		file_v1_email_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_v1_email_proto_rawDesc), len(file_v1_email_proto_rawDesc)))
	})
	return file_v1_email_proto_rawDescData
}

var file_v1_email_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_v1_email_proto_goTypes = []any{
	(*EmailMessage)(nil),             // 0: api.email.v1.EmailMessage
	(*Attachment)(nil),               // 1: api.email.v1.Attachment
	(*Campaign)(nil),                 // 2: api.email.v1.Campaign
	(*CampaignStats)(nil),            // 3: api.email.v1.CampaignStats
	(*SendEmailRequest)(nil),         // 4: api.email.v1.SendEmailRequest
	(*SendEmailResponse)(nil),        // 5: api.email.v1.SendEmailResponse
	(*ListEmailsRequest)(nil),        // 6: api.email.v1.ListEmailsRequest
	(*ListEmailsResponse)(nil),       // 7: api.email.v1.ListEmailsResponse
	(*CreateCampaignRequest)(nil),    // 8: api.email.v1.CreateCampaignRequest
	(*CreateCampaignResponse)(nil),   // 9: api.email.v1.CreateCampaignResponse
	(*GetCampaignStatsRequest)(nil),  // 10: api.email.v1.GetCampaignStatsRequest
	(*GetCampaignStatsResponse)(nil), // 11: api.email.v1.GetCampaignStatsResponse
	(*AnalyzeSentimentRequest)(nil),  // 12: api.email.v1.AnalyzeSentimentRequest
	(*AnalyzeSentimentResponse)(nil), // 13: api.email.v1.AnalyzeSentimentResponse
	nil,                              // 14: api.email.v1.EmailMessage.HeadersEntry
	(*timestamppb.Timestamp)(nil),    // 15: google.protobuf.Timestamp
}
var file_v1_email_proto_depIdxs = []int32{
	1,  // 0: api.email.v1.EmailMessage.attachments:type_name -> api.email.v1.Attachment
	14, // 1: api.email.v1.EmailMessage.headers:type_name -> api.email.v1.EmailMessage.HeadersEntry
	15, // 2: api.email.v1.EmailMessage.timestamp:type_name -> google.protobuf.Timestamp
	15, // 3: api.email.v1.Campaign.scheduled_at:type_name -> google.protobuf.Timestamp
	3,  // 4: api.email.v1.Campaign.stats:type_name -> api.email.v1.CampaignStats
	1,  // 5: api.email.v1.SendEmailRequest.attachments:type_name -> api.email.v1.Attachment
	0,  // 6: api.email.v1.ListEmailsResponse.emails:type_name -> api.email.v1.EmailMessage
	15, // 7: api.email.v1.CreateCampaignRequest.scheduled_at:type_name -> google.protobuf.Timestamp
	2,  // 8: api.email.v1.CreateCampaignResponse.campaign:type_name -> api.email.v1.Campaign
	3,  // 9: api.email.v1.GetCampaignStatsResponse.stats:type_name -> api.email.v1.CampaignStats
	4,  // 10: api.email.v1.EmailService.SendEmail:input_type -> api.email.v1.SendEmailRequest
	6,  // 11: api.email.v1.EmailService.ListEmails:input_type -> api.email.v1.ListEmailsRequest
	8,  // 12: api.email.v1.EmailService.CreateCampaign:input_type -> api.email.v1.CreateCampaignRequest
	10, // 13: api.email.v1.EmailService.GetCampaignStats:input_type -> api.email.v1.GetCampaignStatsRequest
	12, // 14: api.email.v1.EmailService.AnalyzeSentiment:input_type -> api.email.v1.AnalyzeSentimentRequest
	5,  // 15: api.email.v1.EmailService.SendEmail:output_type -> api.email.v1.SendEmailResponse
	7,  // 16: api.email.v1.EmailService.ListEmails:output_type -> api.email.v1.ListEmailsResponse
	9,  // 17: api.email.v1.EmailService.CreateCampaign:output_type -> api.email.v1.CreateCampaignResponse
	11, // 18: api.email.v1.EmailService.GetCampaignStats:output_type -> api.email.v1.GetCampaignStatsResponse
	13, // 19: api.email.v1.EmailService.AnalyzeSentiment:output_type -> api.email.v1.AnalyzeSentimentResponse
	15, // [15:20] is the sub-list for method output_type
	10, // [10:15] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_v1_email_proto_init() }
func file_v1_email_proto_init() {
	if File_v1_email_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_v1_email_proto_rawDesc), len(file_v1_email_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_v1_email_proto_goTypes,
		DependencyIndexes: file_v1_email_proto_depIdxs,
		MessageInfos:      file_v1_email_proto_msgTypes,
	}.Build()
	File_v1_email_proto = out.File
	file_v1_email_proto_goTypes = nil
	file_v1_email_proto_depIdxs = nil
}
