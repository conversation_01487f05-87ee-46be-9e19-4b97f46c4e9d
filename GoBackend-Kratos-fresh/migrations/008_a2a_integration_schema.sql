-- 🌐 A2A Integration Schema - Database Foundation for Agent-to-Agent Protocol
-- Migration 008: A2A Integration with Email Analysis and Interface

-- ============================================================================
-- A2A CORE TABLES
-- ============================================================================

-- A2A Conversations - Persistent conversation contexts
CREATE TABLE a2a_conversations (
    id BIGSERIAL PRIMARY KEY,
    context_id VARCHAR(255) UNIQUE NOT NULL,
    customer_id BIGINT REFERENCES customers(id),
    agent_name VARCHAR(255) NOT NULL DEFAULT 'HVAC CRM Intelligence Agent',
    status VARCHAR(50) NOT NULL DEFAULT 'active', -- active, completed, archived
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    
    -- Indexes
    INDEX idx_a2a_conversations_context_id (context_id),
    INDEX idx_a2a_conversations_customer_id (customer_id),
    INDEX idx_a2a_conversations_status (status),
    INDEX idx_a2a_conversations_created_at (created_at)
);

-- A2A Tasks - Task lifecycle management
CREATE TABLE a2a_tasks (
    id BIGSERIAL PRIMARY KEY,
    task_id VARCHAR(255) UNIQUE NOT NULL,
    conversation_id BIGINT REFERENCES a2a_conversations(id),
    skill_name VARCHAR(255) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'submitted', -- submitted, working, input-required, completed, failed, canceled
    priority VARCHAR(20) DEFAULT 'medium', -- low, medium, high, critical
    input_data JSONB NOT NULL DEFAULT '{}',
    output_data JSONB DEFAULT '{}',
    error_message TEXT,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}',
    
    -- Indexes
    INDEX idx_a2a_tasks_task_id (task_id),
    INDEX idx_a2a_tasks_conversation_id (conversation_id),
    INDEX idx_a2a_tasks_skill_name (skill_name),
    INDEX idx_a2a_tasks_status (status),
    INDEX idx_a2a_tasks_priority (priority),
    INDEX idx_a2a_tasks_created_at (created_at)
);

-- A2A Messages - Message history within conversations
CREATE TABLE a2a_messages (
    id BIGSERIAL PRIMARY KEY,
    message_id VARCHAR(255) UNIQUE NOT NULL,
    conversation_id BIGINT REFERENCES a2a_conversations(id),
    task_id BIGINT REFERENCES a2a_tasks(id),
    role VARCHAR(20) NOT NULL, -- user, agent
    content JSONB NOT NULL, -- message parts
    token_usage JSONB, -- input/output token counts
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}',
    
    -- Indexes
    INDEX idx_a2a_messages_message_id (message_id),
    INDEX idx_a2a_messages_conversation_id (conversation_id),
    INDEX idx_a2a_messages_task_id (task_id),
    INDEX idx_a2a_messages_role (role),
    INDEX idx_a2a_messages_created_at (created_at)
);

-- A2A Skill Executions - Tracking skill usage and performance
CREATE TABLE a2a_skill_executions (
    id BIGSERIAL PRIMARY KEY,
    execution_id VARCHAR(255) UNIQUE NOT NULL,
    task_id BIGINT REFERENCES a2a_tasks(id),
    skill_name VARCHAR(255) NOT NULL,
    execution_type VARCHAR(50) NOT NULL, -- direct, mcp_bridge, langchain_bridge, fallback
    input_size INTEGER DEFAULT 0,
    output_size INTEGER DEFAULT 0,
    execution_time_ms INTEGER DEFAULT 0,
    success BOOLEAN DEFAULT FALSE,
    error_code VARCHAR(50),
    error_message TEXT,
    bridge_tool VARCHAR(255), -- MCP tool or LangChain chain used
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}',
    
    -- Indexes
    INDEX idx_a2a_skill_executions_execution_id (execution_id),
    INDEX idx_a2a_skill_executions_task_id (task_id),
    INDEX idx_a2a_skill_executions_skill_name (skill_name),
    INDEX idx_a2a_skill_executions_execution_type (execution_type),
    INDEX idx_a2a_skill_executions_success (success),
    INDEX idx_a2a_skill_executions_created_at (created_at)
);

-- ============================================================================
-- EMAIL-A2A INTEGRATION TABLES
-- ============================================================================

-- Email A2A Processing - Links emails to A2A tasks
CREATE TABLE email_a2a_processing (
    id BIGSERIAL PRIMARY KEY,
    email_id VARCHAR(255) NOT NULL, -- from email intelligence
    conversation_id BIGINT REFERENCES a2a_conversations(id),
    task_id BIGINT REFERENCES a2a_tasks(id),
    processing_status VARCHAR(50) NOT NULL DEFAULT 'pending', -- pending, processing, completed, failed
    intent_classification VARCHAR(255),
    confidence_score DECIMAL(5,4), -- 0.0000 to 1.0000
    extracted_entities JSONB DEFAULT '{}',
    automated_response BOOLEAN DEFAULT FALSE,
    response_sent_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}',
    
    -- Indexes
    INDEX idx_email_a2a_processing_email_id (email_id),
    INDEX idx_email_a2a_processing_conversation_id (conversation_id),
    INDEX idx_email_a2a_processing_task_id (task_id),
    INDEX idx_email_a2a_processing_status (processing_status),
    INDEX idx_email_a2a_processing_intent (intent_classification),
    INDEX idx_email_a2a_processing_created_at (created_at)
);

-- Transcription A2A Analysis - Links transcriptions to A2A insights
CREATE TABLE transcription_a2a_analysis (
    id BIGSERIAL PRIMARY KEY,
    transcription_id VARCHAR(255) NOT NULL,
    conversation_id BIGINT REFERENCES a2a_conversations(id),
    task_id BIGINT REFERENCES a2a_tasks(id),
    analysis_type VARCHAR(50) NOT NULL, -- sentiment, intent, entity_extraction, summary
    analysis_result JSONB NOT NULL,
    confidence_score DECIMAL(5,4),
    actionable_insights JSONB DEFAULT '{}',
    follow_up_actions JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}',
    
    -- Indexes
    INDEX idx_transcription_a2a_analysis_transcription_id (transcription_id),
    INDEX idx_transcription_a2a_analysis_conversation_id (conversation_id),
    INDEX idx_transcription_a2a_analysis_task_id (task_id),
    INDEX idx_transcription_a2a_analysis_type (analysis_type),
    INDEX idx_transcription_a2a_analysis_created_at (created_at)
);

-- Customer A2A Interactions - Customer-specific A2A history
CREATE TABLE customer_a2a_interactions (
    id BIGSERIAL PRIMARY KEY,
    customer_id BIGINT REFERENCES customers(id),
    conversation_id BIGINT REFERENCES a2a_conversations(id),
    interaction_type VARCHAR(50) NOT NULL, -- inquiry, diagnostic, quote, scheduling, support
    interaction_summary TEXT,
    satisfaction_score INTEGER, -- 1-5 rating
    resolution_status VARCHAR(50) DEFAULT 'open', -- open, in_progress, resolved, escalated
    business_value DECIMAL(10,2), -- estimated value generated
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    resolved_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    
    -- Indexes
    INDEX idx_customer_a2a_interactions_customer_id (customer_id),
    INDEX idx_customer_a2a_interactions_conversation_id (conversation_id),
    INDEX idx_customer_a2a_interactions_type (interaction_type),
    INDEX idx_customer_a2a_interactions_status (resolution_status),
    INDEX idx_customer_a2a_interactions_created_at (created_at)
);

-- ============================================================================
-- WORKFLOW AND AUTOMATION TABLES
-- ============================================================================

-- A2A Workflows - Automated workflows triggered by A2A
CREATE TABLE a2a_workflows (
    id BIGSERIAL PRIMARY KEY,
    workflow_id VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    trigger_type VARCHAR(50) NOT NULL, -- email, task_completion, schedule, webhook
    trigger_conditions JSONB NOT NULL DEFAULT '{}',
    workflow_steps JSONB NOT NULL DEFAULT '{}',
    status VARCHAR(50) NOT NULL DEFAULT 'active', -- active, paused, disabled
    execution_count INTEGER DEFAULT 0,
    success_count INTEGER DEFAULT 0,
    last_executed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}',
    
    -- Indexes
    INDEX idx_a2a_workflows_workflow_id (workflow_id),
    INDEX idx_a2a_workflows_trigger_type (trigger_type),
    INDEX idx_a2a_workflows_status (status),
    INDEX idx_a2a_workflows_created_at (created_at)
);

-- A2A Workflow Executions - Track workflow runs
CREATE TABLE a2a_workflow_executions (
    id BIGSERIAL PRIMARY KEY,
    execution_id VARCHAR(255) UNIQUE NOT NULL,
    workflow_id BIGINT REFERENCES a2a_workflows(id),
    trigger_data JSONB DEFAULT '{}',
    execution_status VARCHAR(50) NOT NULL DEFAULT 'running', -- running, completed, failed, canceled
    steps_completed INTEGER DEFAULT 0,
    total_steps INTEGER DEFAULT 0,
    execution_time_ms INTEGER DEFAULT 0,
    error_message TEXT,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    
    -- Indexes
    INDEX idx_a2a_workflow_executions_execution_id (execution_id),
    INDEX idx_a2a_workflow_executions_workflow_id (workflow_id),
    INDEX idx_a2a_workflow_executions_status (execution_status),
    INDEX idx_a2a_workflow_executions_started_at (started_at)
);

-- ============================================================================
-- METRICS AND ANALYTICS TABLES
-- ============================================================================

-- A2A Metrics - Performance and usage metrics
CREATE TABLE a2a_metrics (
    id BIGSERIAL PRIMARY KEY,
    metric_type VARCHAR(50) NOT NULL, -- request_count, skill_usage, response_time, error_rate
    metric_name VARCHAR(255) NOT NULL,
    metric_value DECIMAL(15,6) NOT NULL,
    dimensions JSONB DEFAULT '{}', -- additional metric dimensions
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    aggregation_period VARCHAR(20) DEFAULT 'hour', -- minute, hour, day, week, month
    
    -- Indexes
    INDEX idx_a2a_metrics_type (metric_type),
    INDEX idx_a2a_metrics_name (metric_name),
    INDEX idx_a2a_metrics_timestamp (timestamp),
    INDEX idx_a2a_metrics_period (aggregation_period)
);

-- ============================================================================
-- VIEWS FOR ANALYTICS
-- ============================================================================

-- A2A Performance Summary View
CREATE VIEW a2a_performance_summary AS
SELECT 
    DATE_TRUNC('day', created_at) as date,
    skill_name,
    COUNT(*) as total_executions,
    COUNT(*) FILTER (WHERE success = true) as successful_executions,
    ROUND(AVG(execution_time_ms), 2) as avg_execution_time_ms,
    ROUND((COUNT(*) FILTER (WHERE success = true) * 100.0 / COUNT(*)), 2) as success_rate
FROM a2a_skill_executions
GROUP BY DATE_TRUNC('day', created_at), skill_name
ORDER BY date DESC, skill_name;

-- Customer A2A Engagement View
CREATE VIEW customer_a2a_engagement AS
SELECT 
    c.id as customer_id,
    c.name as customer_name,
    COUNT(DISTINCT cai.conversation_id) as total_conversations,
    COUNT(cai.id) as total_interactions,
    AVG(cai.satisfaction_score) as avg_satisfaction,
    SUM(cai.business_value) as total_business_value,
    MAX(cai.created_at) as last_interaction_at
FROM customers c
LEFT JOIN customer_a2a_interactions cai ON c.id = cai.customer_id
GROUP BY c.id, c.name
ORDER BY total_interactions DESC;

-- Email to A2A Conversion View
CREATE VIEW email_a2a_conversion AS
SELECT 
    DATE_TRUNC('day', created_at) as date,
    intent_classification,
    COUNT(*) as total_emails,
    COUNT(*) FILTER (WHERE processing_status = 'completed') as processed_emails,
    COUNT(*) FILTER (WHERE automated_response = true) as automated_responses,
    ROUND(AVG(confidence_score), 4) as avg_confidence
FROM email_a2a_processing
GROUP BY DATE_TRUNC('day', created_at), intent_classification
ORDER BY date DESC, intent_classification;
