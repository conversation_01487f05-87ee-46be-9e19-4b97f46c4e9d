package interface

import (
	"context"
	"encoding/json"
	"net/http"
	"strconv"
	"time"

	"gobackend-hvac-kratos/internal/a2a/database"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/gorilla/mux"
	"github.com/gorilla/websocket"
)

// 📊 A2A Interface Dashboard - Real-time A2A monitoring and visualization
// Provides web interface for A2A conversations, tasks, and metrics

type A2ADashboard struct {
	logger    *log.Helper
	a2aRepo   *database.A2ARepository
	upgrader  websocket.Upgrader
	wsClients map[string]*websocket.Conn
}

// NewA2ADashboard creates a new A2A dashboard interface
func NewA2ADashboard(a2aRepo *database.A2ARepository, logger log.Logger) *A2ADashboard {
	logHelper := log.NewHelper(logger)
	logHelper.Info("📊 Initializing A2A Dashboard Interface")

	return &A2ADashboard{
		logger:  logHelper,
		a2aRepo: a2aRepo,
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				return true // Allow all origins in development
			},
		},
		wsClients: make(map[string]*websocket.Conn),
	}
}

// SetupRoutes configures HTTP routes for A2A dashboard
func (d *A2ADashboard) SetupRoutes(router *mux.Router) {
	d.logger.Info("🔧 Setting up A2A dashboard routes")

	// Dashboard API routes
	apiRouter := router.PathPrefix("/api/a2a").Subrouter()
	
	// Conversation management
	apiRouter.HandleFunc("/conversations", d.handleGetConversations).Methods("GET")
	apiRouter.HandleFunc("/conversations/{id}", d.handleGetConversation).Methods("GET")
	apiRouter.HandleFunc("/conversations/{id}/messages", d.handleGetConversationMessages).Methods("GET")
	
	// Task management
	apiRouter.HandleFunc("/tasks", d.handleGetTasks).Methods("GET")
	apiRouter.HandleFunc("/tasks/{id}", d.handleGetTask).Methods("GET")
	
	// Metrics and analytics
	apiRouter.HandleFunc("/metrics/overview", d.handleGetMetricsOverview).Methods("GET")
	apiRouter.HandleFunc("/metrics/skills", d.handleGetSkillMetrics).Methods("GET")
	apiRouter.HandleFunc("/metrics/performance", d.handleGetPerformanceMetrics).Methods("GET")
	
	// Email processing pipeline
	apiRouter.HandleFunc("/email-processing", d.handleGetEmailProcessing).Methods("GET")
	apiRouter.HandleFunc("/email-processing/{id}", d.handleGetEmailProcessingDetail).Methods("GET")
	
	// Real-time WebSocket
	apiRouter.HandleFunc("/ws", d.handleWebSocket).Methods("GET")
	
	// Dashboard views
	router.HandleFunc("/dashboard/a2a", d.handleDashboardView).Methods("GET")
	router.HandleFunc("/dashboard/a2a/conversations", d.handleConversationsView).Methods("GET")
	router.HandleFunc("/dashboard/a2a/analytics", d.handleAnalyticsView).Methods("GET")

	d.logger.Info("✅ A2A dashboard routes configured")
}

// ============================================================================
// CONVERSATION HANDLERS
// ============================================================================

func (d *A2ADashboard) handleGetConversations(w http.ResponseWriter, r *http.Request) {
	d.logger.Info("📋 Getting A2A conversations")

	// Parse query parameters
	page, _ := strconv.Atoi(r.URL.Query().Get("page"))
	if page <= 0 {
		page = 1
	}
	
	limit, _ := strconv.Atoi(r.URL.Query().Get("limit"))
	if limit <= 0 || limit > 100 {
		limit = 20
	}

	status := r.URL.Query().Get("status")
	customerID := r.URL.Query().Get("customer_id")

	// Get conversations from database
	conversations, err := d.getConversations(r.Context(), page, limit, status, customerID)
	if err != nil {
		d.logger.Errorf("Failed to get conversations: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	// Return JSON response
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"conversations": conversations,
		"pagination": map[string]interface{}{
			"page":  page,
			"limit": limit,
			"total": len(conversations), // In real implementation, get actual count
		},
	})
}

func (d *A2ADashboard) handleGetConversation(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	conversationID := vars["id"]

	d.logger.Infof("📋 Getting A2A conversation: %s", conversationID)

	conversation, err := d.a2aRepo.GetConversation(r.Context(), conversationID)
	if err != nil {
		d.logger.Errorf("Failed to get conversation: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	if conversation == nil {
		http.Error(w, "Conversation not found", http.StatusNotFound)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(conversation)
}

func (d *A2ADashboard) handleGetConversationMessages(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	conversationID := vars["id"]

	limit, _ := strconv.Atoi(r.URL.Query().Get("limit"))
	if limit <= 0 || limit > 100 {
		limit = 50
	}

	d.logger.Infof("💬 Getting messages for conversation: %s", conversationID)

	messages, err := d.a2aRepo.GetConversationMessages(r.Context(), conversationID, limit)
	if err != nil {
		d.logger.Errorf("Failed to get messages: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"messages": messages,
		"count":    len(messages),
	})
}

// ============================================================================
// TASK HANDLERS
// ============================================================================

func (d *A2ADashboard) handleGetTasks(w http.ResponseWriter, r *http.Request) {
	d.logger.Info("📋 Getting A2A tasks")

	// Parse query parameters
	status := r.URL.Query().Get("status")
	skillName := r.URL.Query().Get("skill")
	page, _ := strconv.Atoi(r.URL.Query().Get("page"))
	if page <= 0 {
		page = 1
	}

	// Get tasks (placeholder implementation)
	tasks := d.getTasksPlaceholder(status, skillName, page)

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"tasks": tasks,
		"pagination": map[string]interface{}{
			"page":  page,
			"total": len(tasks),
		},
	})
}

func (d *A2ADashboard) handleGetTask(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	taskID := vars["id"]

	d.logger.Infof("📋 Getting A2A task: %s", taskID)

	task, err := d.a2aRepo.GetTask(r.Context(), taskID)
	if err != nil {
		d.logger.Errorf("Failed to get task: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	if task == nil {
		http.Error(w, "Task not found", http.StatusNotFound)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(task)
}

// ============================================================================
// METRICS HANDLERS
// ============================================================================

func (d *A2ADashboard) handleGetMetricsOverview(w http.ResponseWriter, r *http.Request) {
	d.logger.Info("📊 Getting A2A metrics overview")

	// Generate metrics overview
	overview := map[string]interface{}{
		"total_conversations": 156,
		"active_conversations": 23,
		"total_tasks": 342,
		"completed_tasks": 298,
		"failed_tasks": 12,
		"average_response_time": "2.3s",
		"success_rate": 95.2,
		"top_skills": []map[string]interface{}{
			{"name": "customer_inquiry", "usage": 89},
			{"name": "hvac_diagnostics", "usage": 67},
			{"name": "scheduling_assistance", "usage": 45},
		},
		"recent_activity": d.getRecentActivityPlaceholder(),
		"timestamp": time.Now().UTC().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(overview)
}

func (d *A2ADashboard) handleGetSkillMetrics(w http.ResponseWriter, r *http.Request) {
	d.logger.Info("🎯 Getting A2A skill metrics")

	timeRange := r.URL.Query().Get("range")
	if timeRange == "" {
		timeRange = "24h"
	}

	skillMetrics := map[string]interface{}{
		"time_range": timeRange,
		"skills": []map[string]interface{}{
			{
				"name": "hvac_diagnostics",
				"executions": 67,
				"success_rate": 94.0,
				"avg_execution_time": "1.8s",
				"errors": 4,
			},
			{
				"name": "customer_inquiry",
				"executions": 89,
				"success_rate": 98.9,
				"avg_execution_time": "0.9s",
				"errors": 1,
			},
			{
				"name": "equipment_status",
				"executions": 34,
				"success_rate": 91.2,
				"avg_execution_time": "2.1s",
				"errors": 3,
			},
		},
		"timestamp": time.Now().UTC().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(skillMetrics)
}

func (d *A2ADashboard) handleGetPerformanceMetrics(w http.ResponseWriter, r *http.Request) {
	d.logger.Info("⚡ Getting A2A performance metrics")

	performanceMetrics := map[string]interface{}{
		"response_times": map[string]interface{}{
			"p50": "1.2s",
			"p95": "3.8s",
			"p99": "7.2s",
		},
		"throughput": map[string]interface{}{
			"requests_per_minute": 45,
			"peak_rpm": 89,
		},
		"error_rates": map[string]interface{}{
			"total_errors": 12,
			"error_rate": 4.8,
			"error_breakdown": map[string]int{
				"timeout": 5,
				"validation": 3,
				"internal": 4,
			},
		},
		"resource_usage": map[string]interface{}{
			"cpu_usage": "23%",
			"memory_usage": "456MB",
			"active_connections": 15,
		},
		"timestamp": time.Now().UTC().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(performanceMetrics)
}

// ============================================================================
// EMAIL PROCESSING HANDLERS
// ============================================================================

func (d *A2ADashboard) handleGetEmailProcessing(w http.ResponseWriter, r *http.Request) {
	d.logger.Info("📧 Getting email processing status")

	emailProcessing := map[string]interface{}{
		"total_emails_processed": 234,
		"automated_responses": 189,
		"manual_review_required": 12,
		"processing_rate": 95.2,
		"average_processing_time": "3.4s",
		"recent_emails": d.getRecentEmailsPlaceholder(),
		"timestamp": time.Now().UTC().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(emailProcessing)
}

func (d *A2ADashboard) handleGetEmailProcessingDetail(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	emailID := vars["id"]

	d.logger.Infof("📧 Getting email processing detail: %s", emailID)

	// Placeholder implementation
	detail := map[string]interface{}{
		"email_id": emailID,
		"status": "completed",
		"processing_time": "2.1s",
		"intent": "repair_request",
		"confidence": 0.92,
		"skills_triggered": []string{"hvac_diagnostics", "customer_inquiry"},
		"automated_response": true,
		"timestamp": time.Now().UTC().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(detail)
}

// ============================================================================
// WEBSOCKET HANDLER
// ============================================================================

func (d *A2ADashboard) handleWebSocket(w http.ResponseWriter, r *http.Request) {
	d.logger.Info("🔌 New A2A WebSocket connection")

	conn, err := d.upgrader.Upgrade(w, r, nil)
	if err != nil {
		d.logger.Errorf("WebSocket upgrade failed: %v", err)
		return
	}
	defer conn.Close()

	clientID := r.Header.Get("X-Client-ID")
	if clientID == "" {
		clientID = "client-" + strconv.FormatInt(time.Now().UnixNano(), 10)
	}

	d.wsClients[clientID] = conn

	// Send initial data
	d.sendWebSocketMessage(conn, "connected", map[string]interface{}{
		"client_id": clientID,
		"timestamp": time.Now().UTC().Format(time.RFC3339),
	})

	// Handle incoming messages
	for {
		var msg map[string]interface{}
		if err := conn.ReadJSON(&msg); err != nil {
			d.logger.Errorf("WebSocket read error: %v", err)
			break
		}

		// Handle message based on type
		if msgType, ok := msg["type"].(string); ok {
			switch msgType {
			case "subscribe":
				d.handleWebSocketSubscribe(conn, msg)
			case "ping":
				d.sendWebSocketMessage(conn, "pong", map[string]interface{}{
					"timestamp": time.Now().UTC().Format(time.RFC3339),
				})
			}
		}
	}

	delete(d.wsClients, clientID)
	d.logger.Infof("🔌 A2A WebSocket connection closed: %s", clientID)
}

// ============================================================================
// VIEW HANDLERS
// ============================================================================

func (d *A2ADashboard) handleDashboardView(w http.ResponseWriter, r *http.Request) {
	// Serve A2A dashboard HTML
	html := d.generateDashboardHTML()
	w.Header().Set("Content-Type", "text/html")
	w.Write([]byte(html))
}

func (d *A2ADashboard) handleConversationsView(w http.ResponseWriter, r *http.Request) {
	// Serve conversations view HTML
	html := d.generateConversationsHTML()
	w.Header().Set("Content-Type", "text/html")
	w.Write([]byte(html))
}

func (d *A2ADashboard) handleAnalyticsView(w http.ResponseWriter, r *http.Request) {
	// Serve analytics view HTML
	html := d.generateAnalyticsHTML()
	w.Header().Set("Content-Type", "text/html")
	w.Write([]byte(html))
}

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

func (d *A2ADashboard) getConversations(ctx context.Context, page, limit int, status, customerID string) ([]interface{}, error) {
	// Placeholder implementation - replace with actual database query
	return []interface{}{
		map[string]interface{}{
			"id": "conv-001",
			"customer_id": "cust-123",
			"status": "active",
			"created_at": time.Now().Add(-2 * time.Hour).Format(time.RFC3339),
			"message_count": 5,
		},
		map[string]interface{}{
			"id": "conv-002",
			"customer_id": "cust-456",
			"status": "completed",
			"created_at": time.Now().Add(-1 * time.Hour).Format(time.RFC3339),
			"message_count": 3,
		},
	}, nil
}

func (d *A2ADashboard) getTasksPlaceholder(status, skillName string, page int) []interface{} {
	return []interface{}{
		map[string]interface{}{
			"id": "task-001",
			"skill_name": "hvac_diagnostics",
			"status": "completed",
			"created_at": time.Now().Add(-30 * time.Minute).Format(time.RFC3339),
			"execution_time": "2.1s",
		},
		map[string]interface{}{
			"id": "task-002",
			"skill_name": "customer_inquiry",
			"status": "working",
			"created_at": time.Now().Add(-5 * time.Minute).Format(time.RFC3339),
			"execution_time": null,
		},
	}
}

func (d *A2ADashboard) getRecentActivityPlaceholder() []interface{} {
	return []interface{}{
		map[string]interface{}{
			"type": "task_completed",
			"description": "HVAC diagnostics completed for customer ABC Corp",
			"timestamp": time.Now().Add(-10 * time.Minute).Format(time.RFC3339),
		},
		map[string]interface{}{
			"type": "email_processed",
			"description": "Automated response sent for quote request",
			"timestamp": time.Now().Add(-15 * time.Minute).Format(time.RFC3339),
		},
	}
}

func (d *A2ADashboard) getRecentEmailsPlaceholder() []interface{} {
	return []interface{}{
		map[string]interface{}{
			"id": "email-001",
			"from": "<EMAIL>",
			"subject": "HVAC Repair Request",
			"intent": "repair_request",
			"status": "processed",
			"timestamp": time.Now().Add(-20 * time.Minute).Format(time.RFC3339),
		},
	}
}

func (d *A2ADashboard) sendWebSocketMessage(conn *websocket.Conn, msgType string, data interface{}) {
	message := map[string]interface{}{
		"type": msgType,
		"data": data,
	}
	conn.WriteJSON(message)
}

func (d *A2ADashboard) handleWebSocketSubscribe(conn *websocket.Conn, msg map[string]interface{}) {
	// Handle subscription to real-time updates
	d.sendWebSocketMessage(conn, "subscribed", map[string]interface{}{
		"channels": []string{"tasks", "conversations", "metrics"},
	})
}

func (d *A2ADashboard) generateDashboardHTML() string {
	return `<!DOCTYPE html>
<html>
<head>
    <title>A2A Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .metric { display: inline-block; margin: 10px; padding: 20px; border: 1px solid #ccc; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🌐 A2A Agent Dashboard</h1>
    <div id="metrics"></div>
    <script>
        fetch('/api/a2a/metrics/overview')
            .then(r => r.json())
            .then(data => {
                document.getElementById('metrics').innerHTML = 
                    '<div class="metric">Conversations: ' + data.total_conversations + '</div>' +
                    '<div class="metric">Success Rate: ' + data.success_rate + '%</div>' +
                    '<div class="metric">Avg Response: ' + data.average_response_time + '</div>';
            });
    </script>
</body>
</html>`
}

func (d *A2ADashboard) generateConversationsHTML() string {
	return `<!DOCTYPE html>
<html>
<head><title>A2A Conversations</title></head>
<body>
    <h1>💬 A2A Conversations</h1>
    <div id="conversations"></div>
    <script>
        fetch('/api/a2a/conversations')
            .then(r => r.json())
            .then(data => {
                const html = data.conversations.map(c => 
                    '<div>ID: ' + c.id + ' | Status: ' + c.status + ' | Messages: ' + c.message_count + '</div>'
                ).join('');
                document.getElementById('conversations').innerHTML = html;
            });
    </script>
</body>
</html>`
}

func (d *A2ADashboard) generateAnalyticsHTML() string {
	return `<!DOCTYPE html>
<html>
<head><title>A2A Analytics</title></head>
<body>
    <h1>📊 A2A Analytics</h1>
    <div id="analytics"></div>
    <script>
        fetch('/api/a2a/metrics/skills')
            .then(r => r.json())
            .then(data => {
                const html = data.skills.map(s => 
                    '<div>Skill: ' + s.name + ' | Executions: ' + s.executions + ' | Success: ' + s.success_rate + '%</div>'
                ).join('');
                document.getElementById('analytics').innerHTML = html;
            });
    </script>
</body>
</html>`
}
