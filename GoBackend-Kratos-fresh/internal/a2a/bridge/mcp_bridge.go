package bridge

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"gobackend-hvac-kratos/internal/a2a"

	"github.com/go-kratos/kratos/v2/log"
)

// 🌉 MCP Bridge - A2A to MCP Integration
// Connects Google A2A protocol with Model Context Protocol for enhanced AI capabilities

type MCPBridge struct {
	logger     *log.Helper
	mcpClient  MCPClient
	skillMap   map[string]string // A2A skill -> MCP tool mapping
}

// MCPClient interface for MCP server communication
type MCPClient interface {
	CallTool(ctx context.Context, toolName string, arguments map[string]interface{}) (*MCPToolResult, error)
	ListTools(ctx context.Context) ([]*MCPTool, error)
	GetServerInfo(ctx context.Context) (*MCPServerInfo, error)
}

// MCPTool represents an MCP tool
type MCPTool struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	InputSchema map[string]interface{} `json:"inputSchema"`
}

// MCPToolResult represents the result of an MCP tool call
type MCPToolResult struct {
	Content []MCPContent `json:"content"`
	IsError bool         `json:"isError"`
}

// MCPContent represents content in MCP responses
type MCPContent struct {
	Type string `json:"type"`
	Text string `json:"text,omitempty"`
	Data interface{} `json:"data,omitempty"`
}

// MCPServerInfo contains MCP server information
type MCPServerInfo struct {
	Name         string `json:"name"`
	Version      string `json:"version"`
	Capabilities map[string]interface{} `json:"capabilities"`
}

// NewMCPBridge creates a new MCP bridge
func NewMCPBridge(mcpClient MCPClient, logger log.Logger) *MCPBridge {
	logHelper := log.NewHelper(logger)
	logHelper.Info("🌉 Initializing MCP Bridge for A2A integration")

	bridge := &MCPBridge{
		logger:    logHelper,
		mcpClient: mcpClient,
		skillMap:  make(map[string]string),
	}

	// Initialize skill mappings
	bridge.initializeSkillMappings()

	logHelper.Info("✅ MCP Bridge initialized successfully")
	return bridge
}

// ExecuteSkill executes an A2A skill using MCP tools
func (b *MCPBridge) ExecuteSkill(ctx context.Context, skillName string, input map[string]interface{}) (interface{}, error) {
	b.logger.Infof("🔧 Executing A2A skill '%s' via MCP", skillName)

	// Map A2A skill to MCP tool
	mcpTool, exists := b.skillMap[skillName]
	if !exists {
		return b.handleUnmappedSkill(ctx, skillName, input)
	}

	// Execute MCP tool
	result, err := b.mcpClient.CallTool(ctx, mcpTool, input)
	if err != nil {
		b.logger.Errorf("Failed to execute MCP tool '%s': %v", mcpTool, err)
		return nil, fmt.Errorf("MCP tool execution failed: %w", err)
	}

	// Convert MCP result to A2A format
	return b.convertMCPResultToA2A(result)
}

// initializeSkillMappings sets up A2A skill to MCP tool mappings
func (b *MCPBridge) initializeSkillMappings() {
	b.logger.Info("🗺️ Initializing A2A to MCP skill mappings")

	// HVAC Diagnostics Skills
	b.skillMap["hvac_diagnostics"] = "hvac_system_diagnostics"
	b.skillMap["equipment_status"] = "equipment_health_check"
	b.skillMap["maintenance_planning"] = "maintenance_scheduler"

	// Customer Management Skills
	b.skillMap["customer_inquiry"] = "customer_service_handler"
	b.skillMap["quote_generation"] = "quote_generator"
	b.skillMap["scheduling_assistance"] = "appointment_scheduler"

	// Advanced Skills (for authenticated users)
	b.skillMap["financial_analytics"] = "financial_analyzer"
	b.skillMap["workflow_automation"] = "workflow_engine"
	b.skillMap["data_export"] = "data_exporter"

	// AI-Powered Skills
	b.skillMap["predictive_maintenance"] = "ai_maintenance_predictor"
	b.skillMap["energy_optimization"] = "energy_optimizer"
	b.skillMap["fault_detection"] = "ai_fault_detector"

	b.logger.Infof("✅ Initialized %d skill mappings", len(b.skillMap))
}

// handleUnmappedSkill handles skills that don't have direct MCP mappings
func (b *MCPBridge) handleUnmappedSkill(ctx context.Context, skillName string, input map[string]interface{}) (interface{}, error) {
	b.logger.Warnf("⚠️ No MCP mapping found for skill '%s', using fallback", skillName)

	// Try to find similar MCP tools
	tools, err := b.mcpClient.ListTools(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to list MCP tools: %w", err)
	}

	// Look for similar tool names
	for _, tool := range tools {
		if b.isSkillSimilar(skillName, tool.Name) {
			b.logger.Infof("🔍 Found similar MCP tool '%s' for skill '%s'", tool.Name, skillName)
			result, err := b.mcpClient.CallTool(ctx, tool.Name, input)
			if err != nil {
				continue // Try next tool
			}
			return b.convertMCPResultToA2A(result)
		}
	}

	// Fallback to generic response
	return b.generateFallbackResponse(skillName, input), nil
}

// isSkillSimilar checks if an A2A skill is similar to an MCP tool
func (b *MCPBridge) isSkillSimilar(skillName, toolName string) bool {
	skillWords := strings.Split(strings.ToLower(skillName), "_")
	toolWords := strings.Split(strings.ToLower(toolName), "_")

	// Check for common words
	commonWords := 0
	for _, skillWord := range skillWords {
		for _, toolWord := range toolWords {
			if skillWord == toolWord {
				commonWords++
				break
			}
		}
	}

	// Consider similar if at least 50% of words match
	return float64(commonWords)/float64(len(skillWords)) >= 0.5
}

// convertMCPResultToA2A converts MCP tool results to A2A format
func (b *MCPBridge) convertMCPResultToA2A(result *MCPToolResult) (interface{}, error) {
	if result.IsError {
		return nil, fmt.Errorf("MCP tool returned error")
	}

	// Convert MCP content to A2A artifacts
	artifacts := make([]a2a.Artifact, 0, len(result.Content))
	
	for i, content := range result.Content {
		artifact := a2a.Artifact{
			ArtifactID: fmt.Sprintf("mcp-result-%d", i),
			Name:       fmt.Sprintf("result_%d", i),
			Parts: []a2a.Part{
				{
					Type: content.Type,
					Text: content.Text,
					Data: content.Data,
				},
			},
			Metadata: map[string]interface{}{
				"source":     "mcp",
				"timestamp":  time.Now().UTC().Format(time.RFC3339),
				"content_type": content.Type,
			},
		}
		artifacts = append(artifacts, artifact)
	}

	return map[string]interface{}{
		"artifacts": artifacts,
		"metadata": map[string]interface{}{
			"source":      "mcp_bridge",
			"tool_count":  len(result.Content),
			"processed_at": time.Now().UTC().Format(time.RFC3339),
		},
	}, nil
}

// generateFallbackResponse creates a fallback response for unmapped skills
func (b *MCPBridge) generateFallbackResponse(skillName string, input map[string]interface{}) interface{} {
	b.logger.Infof("🔄 Generating fallback response for skill '%s'", skillName)

	response := map[string]interface{}{
		"skill":   skillName,
		"status":  "processed",
		"message": b.generateSkillSpecificMessage(skillName, input),
		"metadata": map[string]interface{}{
			"source":      "fallback",
			"skill_name":  skillName,
			"processed_at": time.Now().UTC().Format(time.RFC3339),
		},
	}

	// Add skill-specific data
	switch skillName {
	case "hvac_diagnostics":
		response["diagnostics"] = b.generateHVACDiagnostics(input)
	case "customer_inquiry":
		response["inquiry_response"] = b.generateInquiryResponse(input)
	case "equipment_status":
		response["equipment_data"] = b.generateEquipmentStatus(input)
	case "maintenance_planning":
		response["maintenance_plan"] = b.generateMaintenancePlan(input)
	case "quote_generation":
		response["quote"] = b.generateQuote(input)
	case "scheduling_assistance":
		response["schedule"] = b.generateSchedule(input)
	}

	return response
}

// generateSkillSpecificMessage creates contextual messages for different skills
func (b *MCPBridge) generateSkillSpecificMessage(skillName string, input map[string]interface{}) string {
	switch skillName {
	case "hvac_diagnostics":
		return "HVAC system diagnostics completed. Based on the symptoms provided, I've analyzed potential issues and recommendations."
	case "customer_inquiry":
		return "Customer inquiry processed. I've prepared a comprehensive response based on the inquiry type and details provided."
	case "equipment_status":
		return "Equipment status check completed. Current performance metrics and maintenance status have been evaluated."
	case "maintenance_planning":
		return "Maintenance planning completed. I've generated a comprehensive maintenance schedule based on your equipment and requirements."
	case "quote_generation":
		return "Service quote generated. The estimate includes labor, materials, and service details based on your requirements."
	case "scheduling_assistance":
		return "Scheduling assistance provided. I've checked availability and prepared appointment options for your service request."
	default:
		return fmt.Sprintf("Skill '%s' executed successfully. The request has been processed with available information.", skillName)
	}
}

// Skill-specific fallback generators
func (b *MCPBridge) generateHVACDiagnostics(input map[string]interface{}) map[string]interface{} {
	return map[string]interface{}{
		"system_analysis": "Preliminary analysis completed",
		"recommendations": []string{
			"Check air filters and replace if necessary",
			"Inspect refrigerant levels",
			"Verify electrical connections",
			"Schedule professional inspection",
		},
		"priority": "medium",
		"estimated_cost": "150-300 PLN",
	}
}

func (b *MCPBridge) generateInquiryResponse(input map[string]interface{}) map[string]interface{} {
	return map[string]interface{}{
		"response_type": "automated",
		"next_steps": []string{
			"Technical assessment scheduled",
			"Quote preparation in progress",
			"Customer service follow-up planned",
		},
		"estimated_response_time": "24 hours",
	}
}

func (b *MCPBridge) generateEquipmentStatus(input map[string]interface{}) map[string]interface{} {
	return map[string]interface{}{
		"status": "operational",
		"health_score": 85,
		"last_maintenance": "2024-03-15",
		"next_service": "2024-06-15",
		"alerts": []string{},
	}
}

func (b *MCPBridge) generateMaintenancePlan(input map[string]interface{}) map[string]interface{} {
	return map[string]interface{}{
		"plan_type": "preventive",
		"schedule": []map[string]interface{}{
			{
				"task": "Filter replacement",
				"date": "2024-06-01",
				"priority": "high",
			},
			{
				"task": "System inspection",
				"date": "2024-06-15",
				"priority": "medium",
			},
		},
		"total_cost": "500 PLN",
	}
}

func (b *MCPBridge) generateQuote(input map[string]interface{}) map[string]interface{} {
	return map[string]interface{}{
		"quote_id": fmt.Sprintf("Q-%d", time.Now().Unix()),
		"service_type": "HVAC Service",
		"estimated_cost": "800 PLN",
		"labor_hours": 4,
		"materials": "Standard HVAC components",
		"valid_until": time.Now().AddDate(0, 0, 30).Format("2006-01-02"),
	}
}

func (b *MCPBridge) generateSchedule(input map[string]interface{}) map[string]interface{} {
	return map[string]interface{}{
		"available_slots": []map[string]interface{}{
			{
				"date": time.Now().AddDate(0, 0, 1).Format("2006-01-02"),
				"time": "09:00-12:00",
				"technician": "Jan Kowalski",
			},
			{
				"date": time.Now().AddDate(0, 0, 2).Format("2006-01-02"),
				"time": "14:00-17:00",
				"technician": "Anna Nowak",
			},
		},
		"booking_reference": fmt.Sprintf("BK-%d", time.Now().Unix()),
	}
}

// GetSkillMappings returns the current skill mappings
func (b *MCPBridge) GetSkillMappings() map[string]string {
	return b.skillMap
}

// AddSkillMapping adds a new skill mapping
func (b *MCPBridge) AddSkillMapping(skillName, mcpTool string) {
	b.skillMap[skillName] = mcpTool
	b.logger.Infof("➕ Added skill mapping: %s -> %s", skillName, mcpTool)
}

// RemoveSkillMapping removes a skill mapping
func (b *MCPBridge) RemoveSkillMapping(skillName string) {
	delete(b.skillMap, skillName)
	b.logger.Infof("➖ Removed skill mapping: %s", skillName)
}
