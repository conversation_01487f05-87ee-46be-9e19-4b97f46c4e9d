package bridge

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"gobackend-hvac-kratos/internal/a2a"

	"github.com/go-kratos/kratos/v2/log"
)

// 🔗 LangChain Bridge - A2A to LangChain Integration
// Connects Google A2A protocol with LangChain agents for advanced AI workflows

type LangChainBridge struct {
	logger        *log.Helper
	langchainClient LangChainClient
	agentChains   map[string]string // A2A skill -> LangChain chain mapping
	contextStore  map[string]*ConversationContext
}

// LangChainClient interface for LangChain communication
type LangChainClient interface {
	InvokeChain(ctx context.Context, chainName string, input map[string]interface{}) (*LangChainResult, error)
	ListChains(ctx context.Context) ([]*LangChainInfo, error)
	GetChainInfo(ctx context.Context, chainName string) (*LangChainInfo, error)
}

// LangChainResult represents the result of a LangChain invocation
type LangChainResult struct {
	Output   interface{}            `json:"output"`
	Metadata map[string]interface{} `json:"metadata"`
	Tokens   *TokenUsage           `json:"tokens,omitempty"`
	Error    string                `json:"error,omitempty"`
}

// LangChainInfo contains information about a LangChain chain
type LangChainInfo struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	InputSchema map[string]interface{} `json:"input_schema"`
	OutputSchema map[string]interface{} `json:"output_schema"`
	Tags        []string               `json:"tags"`
}

// TokenUsage tracks token consumption
type TokenUsage struct {
	InputTokens  int `json:"input_tokens"`
	OutputTokens int `json:"output_tokens"`
	TotalTokens  int `json:"total_tokens"`
}

// ConversationContext maintains context across A2A interactions
type ConversationContext struct {
	ContextID    string                 `json:"context_id"`
	Messages     []a2a.Message          `json:"messages"`
	Metadata     map[string]interface{} `json:"metadata"`
	CreatedAt    time.Time              `json:"created_at"`
	UpdatedAt    time.Time              `json:"updated_at"`
	TokenUsage   *TokenUsage           `json:"token_usage"`
}

// NewLangChainBridge creates a new LangChain bridge
func NewLangChainBridge(langchainClient LangChainClient, logger log.Logger) *LangChainBridge {
	logHelper := log.NewHelper(logger)
	logHelper.Info("🔗 Initializing LangChain Bridge for A2A integration")

	bridge := &LangChainBridge{
		logger:          logHelper,
		langchainClient: langchainClient,
		agentChains:     make(map[string]string),
		contextStore:    make(map[string]*ConversationContext),
	}

	// Initialize chain mappings
	bridge.initializeChainMappings()

	logHelper.Info("✅ LangChain Bridge initialized successfully")
	return bridge
}

// ProcessMessage processes an A2A message through LangChain
func (b *LangChainBridge) ProcessMessage(ctx context.Context, message *a2a.Message) (*a2a.Message, error) {
	b.logger.Infof("🔄 Processing A2A message through LangChain")

	// Get or create conversation context
	contextID := message.ContextID
	if contextID == "" {
		contextID = fmt.Sprintf("ctx-%d", time.Now().UnixNano())
	}

	conversationCtx := b.getOrCreateContext(contextID)
	conversationCtx.Messages = append(conversationCtx.Messages, *message)
	conversationCtx.UpdatedAt = time.Now()

	// Determine appropriate LangChain chain based on message content
	chainName := b.selectChainForMessage(message)
	
	// Prepare input for LangChain
	input := b.prepareChainInput(message, conversationCtx)

	// Invoke LangChain
	result, err := b.langchainClient.InvokeChain(ctx, chainName, input)
	if err != nil {
		b.logger.Errorf("Failed to invoke LangChain chain '%s': %v", chainName, err)
		return b.generateErrorResponse(message, err), nil
	}

	// Convert LangChain result to A2A message
	response := b.convertLangChainResultToMessage(result, message, conversationCtx)

	// Update context with response
	conversationCtx.Messages = append(conversationCtx.Messages, *response)
	if result.Tokens != nil {
		if conversationCtx.TokenUsage == nil {
			conversationCtx.TokenUsage = &TokenUsage{}
		}
		conversationCtx.TokenUsage.InputTokens += result.Tokens.InputTokens
		conversationCtx.TokenUsage.OutputTokens += result.Tokens.OutputTokens
		conversationCtx.TokenUsage.TotalTokens += result.Tokens.TotalTokens
	}

	return response, nil
}

// initializeChainMappings sets up A2A skill to LangChain chain mappings
func (b *LangChainBridge) initializeChainMappings() {
	b.logger.Info("🗺️ Initializing A2A to LangChain chain mappings")

	// HVAC-specific chains
	b.agentChains["hvac_diagnostics"] = "hvac_diagnostic_agent"
	b.agentChains["equipment_status"] = "equipment_analysis_agent"
	b.agentChains["maintenance_planning"] = "maintenance_planning_agent"

	// Customer service chains
	b.agentChains["customer_inquiry"] = "customer_service_agent"
	b.agentChains["quote_generation"] = "quote_generation_agent"
	b.agentChains["scheduling_assistance"] = "scheduling_agent"

	// Advanced AI chains
	b.agentChains["financial_analytics"] = "financial_analysis_agent"
	b.agentChains["workflow_automation"] = "workflow_automation_agent"
	b.agentChains["predictive_maintenance"] = "predictive_maintenance_agent"

	// General purpose chains
	b.agentChains["general_inquiry"] = "general_hvac_agent"
	b.agentChains["technical_support"] = "technical_support_agent"

	b.logger.Infof("✅ Initialized %d chain mappings", len(b.agentChains))
}

// selectChainForMessage determines the best LangChain chain for a message
func (b *LangChainBridge) selectChainForMessage(message *a2a.Message) string {
	// Extract text content from message parts
	var textContent strings.Builder
	for _, part := range message.Parts {
		if part.Type == "text" {
			textContent.WriteString(part.Text)
			textContent.WriteString(" ")
		}
	}
	
	text := strings.ToLower(textContent.String())

	// Keyword-based chain selection
	if strings.Contains(text, "diagnostic") || strings.Contains(text, "problem") || strings.Contains(text, "issue") {
		return b.agentChains["hvac_diagnostics"]
	}
	if strings.Contains(text, "quote") || strings.Contains(text, "price") || strings.Contains(text, "cost") {
		return b.agentChains["quote_generation"]
	}
	if strings.Contains(text, "schedule") || strings.Contains(text, "appointment") || strings.Contains(text, "book") {
		return b.agentChains["scheduling_assistance"]
	}
	if strings.Contains(text, "maintenance") || strings.Contains(text, "service") {
		return b.agentChains["maintenance_planning"]
	}
	if strings.Contains(text, "status") || strings.Contains(text, "check") || strings.Contains(text, "equipment") {
		return b.agentChains["equipment_status"]
	}

	// Default to general inquiry chain
	return b.agentChains["general_inquiry"]
}

// prepareChainInput prepares input for LangChain invocation
func (b *LangChainBridge) prepareChainInput(message *a2a.Message, ctx *ConversationContext) map[string]interface{} {
	// Extract text content
	var textParts []string
	var dataParts []interface{}
	
	for _, part := range message.Parts {
		switch part.Type {
		case "text":
			textParts = append(textParts, part.Text)
		case "data":
			dataParts = append(dataParts, part.Data)
		}
	}

	input := map[string]interface{}{
		"message": strings.Join(textParts, " "),
		"context_id": ctx.ContextID,
		"timestamp": time.Now().UTC().Format(time.RFC3339),
	}

	// Add conversation history (last 5 messages for context)
	historyLimit := 5
	if len(ctx.Messages) > 1 { // Exclude current message
		start := len(ctx.Messages) - 1 - historyLimit
		if start < 0 {
			start = 0
		}
		
		var history []map[string]interface{}
		for i := start; i < len(ctx.Messages)-1; i++ {
			msg := ctx.Messages[i]
			var msgText strings.Builder
			for _, part := range msg.Parts {
				if part.Type == "text" {
					msgText.WriteString(part.Text)
					msgText.WriteString(" ")
				}
			}
			
			history = append(history, map[string]interface{}{
				"role": msg.Role,
				"content": strings.TrimSpace(msgText.String()),
				"timestamp": msg.Metadata["timestamp"],
			})
		}
		input["conversation_history"] = history
	}

	// Add data parts if any
	if len(dataParts) > 0 {
		input["data"] = dataParts
	}

	// Add metadata
	if message.Metadata != nil {
		input["metadata"] = message.Metadata
	}

	return input
}

// convertLangChainResultToMessage converts LangChain result to A2A message
func (b *LangChainBridge) convertLangChainResultToMessage(result *LangChainResult, originalMessage *a2a.Message, ctx *ConversationContext) *a2a.Message {
	response := &a2a.Message{
		Role:      "agent",
		MessageID: fmt.Sprintf("msg-%d", time.Now().UnixNano()),
		TaskID:    originalMessage.TaskID,
		ContextID: originalMessage.ContextID,
		Metadata: map[string]interface{}{
			"source":      "langchain_bridge",
			"chain_used":  b.selectChainForMessage(originalMessage),
			"timestamp":   time.Now().UTC().Format(time.RFC3339),
			"token_usage": result.Tokens,
		},
	}

	// Handle error in result
	if result.Error != "" {
		response.Parts = []a2a.Part{
			{
				Type: "text",
				Text: fmt.Sprintf("I encountered an issue while processing your request: %s. Please try again or contact support.", result.Error),
				Metadata: map[string]interface{}{
					"error": true,
					"error_message": result.Error,
				},
			},
		}
		return response
	}

	// Convert output to parts
	switch output := result.Output.(type) {
	case string:
		response.Parts = []a2a.Part{
			{
				Type: "text",
				Text: output,
			},
		}
	case map[string]interface{}:
		// Handle structured output
		if text, ok := output["text"].(string); ok {
			response.Parts = append(response.Parts, a2a.Part{
				Type: "text",
				Text: text,
			})
		}
		if data, ok := output["data"]; ok {
			response.Parts = append(response.Parts, a2a.Part{
				Type: "data",
				Data: data,
			})
		}
		// If no text or data, convert entire output to data part
		if len(response.Parts) == 0 {
			response.Parts = []a2a.Part{
				{
					Type: "data",
					Data: output,
				},
			}
		}
	default:
		// Convert any other type to data part
		response.Parts = []a2a.Part{
			{
				Type: "data",
				Data: output,
			},
		}
	}

	// Add metadata from LangChain result
	if result.Metadata != nil {
		if response.Metadata == nil {
			response.Metadata = make(map[string]interface{})
		}
		response.Metadata["langchain_metadata"] = result.Metadata
	}

	return response
}

// generateErrorResponse creates an error response message
func (b *LangChainBridge) generateErrorResponse(originalMessage *a2a.Message, err error) *a2a.Message {
	return &a2a.Message{
		Role:      "agent",
		MessageID: fmt.Sprintf("msg-error-%d", time.Now().UnixNano()),
		TaskID:    originalMessage.TaskID,
		ContextID: originalMessage.ContextID,
		Parts: []a2a.Part{
			{
				Type: "text",
				Text: "I'm sorry, but I encountered a technical issue while processing your request. Our team has been notified and will resolve this shortly. Please try again later.",
				Metadata: map[string]interface{}{
					"error": true,
					"error_details": err.Error(),
				},
			},
		},
		Metadata: map[string]interface{}{
			"source":    "langchain_bridge",
			"error":     true,
			"timestamp": time.Now().UTC().Format(time.RFC3339),
		},
	}
}

// getOrCreateContext retrieves or creates a conversation context
func (b *LangChainBridge) getOrCreateContext(contextID string) *ConversationContext {
	if ctx, exists := b.contextStore[contextID]; exists {
		return ctx
	}

	ctx := &ConversationContext{
		ContextID: contextID,
		Messages:  make([]a2a.Message, 0),
		Metadata:  make(map[string]interface{}),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	b.contextStore[contextID] = ctx
	return ctx
}

// GetConversationContext retrieves a conversation context
func (b *LangChainBridge) GetConversationContext(contextID string) (*ConversationContext, bool) {
	ctx, exists := b.contextStore[contextID]
	return ctx, exists
}

// ClearContext removes a conversation context
func (b *LangChainBridge) ClearContext(contextID string) {
	delete(b.contextStore, contextID)
	b.logger.Infof("🗑️ Cleared conversation context: %s", contextID)
}

// GetChainMappings returns the current chain mappings
func (b *LangChainBridge) GetChainMappings() map[string]string {
	return b.agentChains
}

// AddChainMapping adds a new chain mapping
func (b *LangChainBridge) AddChainMapping(skillName, chainName string) {
	b.agentChains[skillName] = chainName
	b.logger.Infof("➕ Added chain mapping: %s -> %s", skillName, chainName)
}

// RemoveChainMapping removes a chain mapping
func (b *LangChainBridge) RemoveChainMapping(skillName string) {
	delete(b.agentChains, skillName)
	b.logger.Infof("➖ Removed chain mapping: %s", skillName)
}
