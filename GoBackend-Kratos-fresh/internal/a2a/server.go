package a2a

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/uuid"
	"github.com/gorilla/mux"
)

// 🚀 A2A Server - Google Agent-to-Agent Protocol Implementation
// Enterprise-grade A2A server with MCP integration for HVAC CRM

type A2AServer struct {
	// Core components
	logger        *log.Helper
	cardGenerator *AgentCardGenerator
	skillRegistry *SkillRegistry
	taskManager   *TaskManager
	streamManager *StreamManager

	// Configuration
	baseURL     string
	version     string
	environment string

	// Runtime state
	activeTasks   map[string]*Task
	activeStreams map[string]*StreamSession
	tasksMutex    sync.RWMutex
	streamsMutex  sync.RWMutex

	// Integration bridges
	mcpBridge       MCPBridge
	langchainBridge LangChainBridge
}

// A2AServerConfig contains server configuration
type A2AServerConfig struct {
	BaseURL     string
	Version     string
	Environment string
	EnableAuth  bool
	EnableSSE   bool
	EnablePush  bool
}

// NewA2AServer creates a new A2A protocol server
func NewA2AServer(config *A2AServerConfig, logger log.Logger) (*A2AServer, error) {
	logHelper := log.NewHelper(logger)
	logHelper.Info("🚀 Initializing A2A Server for HVAC CRM")

	// Initialize card generator
	cardGen := NewAgentCardGenerator(config.BaseURL, config.Version, config.Environment, logger)

	// Initialize skill registry
	skillRegistry := NewSkillRegistry(logger)

	// Initialize task manager
	taskManager := NewTaskManager(logger)

	// Initialize stream manager
	streamManager := NewStreamManager(logger)

	server := &A2AServer{
		logger:        logHelper,
		cardGenerator: cardGen,
		skillRegistry: skillRegistry,
		taskManager:   taskManager,
		streamManager: streamManager,
		baseURL:       config.BaseURL,
		version:       config.Version,
		environment:   config.Environment,
		activeTasks:   make(map[string]*Task),
		activeStreams: make(map[string]*StreamSession),
	}

	// Register default HVAC skills
	if err := server.registerDefaultSkills(); err != nil {
		return nil, fmt.Errorf("failed to register default skills: %w", err)
	}

	logHelper.Info("✅ A2A Server initialized successfully")
	return server, nil
}

// SetupRoutes configures HTTP routes for A2A endpoints
func (s *A2AServer) SetupRoutes(router *mux.Router) {
	s.logger.Info("🔧 Setting up A2A protocol routes")

	// Agent discovery endpoints
	router.HandleFunc("/.well-known/agent.json", s.handlePublicAgentCard).Methods("GET")
	router.HandleFunc("/a2a/agent/authenticatedExtendedCard", s.handleAuthenticatedExtendedCard).Methods("GET")

	// A2A protocol endpoints
	a2aRouter := router.PathPrefix("/a2a").Subrouter()

	// Core messaging endpoints
	a2aRouter.HandleFunc("/message/send", s.handleMessageSend).Methods("POST")
	a2aRouter.HandleFunc("/message/stream", s.handleMessageStream).Methods("POST")

	// Task management endpoints
	a2aRouter.HandleFunc("/tasks/get", s.handleTasksGet).Methods("POST")
	a2aRouter.HandleFunc("/tasks/cancel", s.handleTasksCancel).Methods("POST")
	a2aRouter.HandleFunc("/tasks/resubscribe", s.handleTasksResubscribe).Methods("POST")

	// Push notification endpoints
	a2aRouter.HandleFunc("/tasks/pushNotificationConfig/set", s.handlePushNotificationConfigSet).Methods("POST")
	a2aRouter.HandleFunc("/tasks/pushNotificationConfig/get", s.handlePushNotificationConfigGet).Methods("POST")

	s.logger.Info("✅ A2A routes configured successfully")
}

// ============================================================================
// AGENT DISCOVERY HANDLERS
// ============================================================================

// handlePublicAgentCard serves the public agent card for discovery
func (s *A2AServer) handlePublicAgentCard(w http.ResponseWriter, r *http.Request) {
	s.logger.Info("📋 Serving public agent card")

	card, err := s.cardGenerator.GeneratePublicAgentCard()
	if err != nil {
		s.logger.Errorf("Failed to generate public agent card: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Cache-Control", "public, max-age=3600") // Cache for 1 hour

	if err := json.NewEncoder(w).Encode(card); err != nil {
		s.logger.Errorf("Failed to encode agent card: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	s.logger.Info("✅ Public agent card served successfully")
}

// handleAuthenticatedExtendedCard serves the extended agent card for authenticated users
func (s *A2AServer) handleAuthenticatedExtendedCard(w http.ResponseWriter, r *http.Request) {
	s.logger.Info("🔐 Serving authenticated extended agent card")

	// TODO: Implement authentication validation
	// For now, we'll serve the extended card

	card, err := s.cardGenerator.GenerateAuthenticatedExtendedCard()
	if err != nil {
		s.logger.Errorf("Failed to generate extended agent card: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Cache-Control", "private, max-age=1800") // Cache for 30 minutes

	if err := json.NewEncoder(w).Encode(card); err != nil {
		s.logger.Errorf("Failed to encode extended agent card: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	s.logger.Info("✅ Extended agent card served successfully")
}

// ============================================================================
// CORE A2A MESSAGE HANDLERS
// ============================================================================

// handleMessageSend processes synchronous message requests
func (s *A2AServer) handleMessageSend(w http.ResponseWriter, r *http.Request) {
	s.logger.Info("📨 Processing A2A message/send request")

	var request JSONRPCRequest
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		s.sendJSONRPCError(w, request.ID, -32700, "Parse error", nil)
		return
	}

	// Validate JSON-RPC structure
	if request.JSONRPC != "2.0" || request.Method != "message/send" {
		s.sendJSONRPCError(w, request.ID, -32600, "Invalid Request", nil)
		return
	}

	// Parse parameters
	var params MessageSendParams
	if err := s.parseParams(request.Params, &params); err != nil {
		s.sendJSONRPCError(w, request.ID, -32602, "Invalid params", err.Error())
		return
	}

	// Process the message
	result, err := s.processMessage(r.Context(), &params)
	if err != nil {
		s.logger.Errorf("Failed to process message: %v", err)
		s.sendJSONRPCError(w, request.ID, -32603, "Internal error", err.Error())
		return
	}

	// Send response
	response := JSONRPCResponse{
		JSONRPC: "2.0",
		ID:      request.ID,
		Result:  result,
	}

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(response); err != nil {
		s.logger.Errorf("Failed to encode response: %v", err)
		return
	}

	s.logger.Info("✅ Message processed successfully")
}

// handleMessageStream processes streaming message requests with SSE
func (s *A2AServer) handleMessageStream(w http.ResponseWriter, r *http.Request) {
	s.logger.Info("🌊 Processing A2A message/stream request")

	var request JSONRPCRequest
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		s.sendJSONRPCError(w, request.ID, -32700, "Parse error", nil)
		return
	}

	// Parse parameters
	var params MessageSendParams
	if err := s.parseParams(request.Params, &params); err != nil {
		s.sendJSONRPCError(w, request.ID, -32602, "Invalid params", err.Error())
		return
	}

	// Setup SSE headers
	w.Header().Set("Content-Type", "text/event-stream")
	w.Header().Set("Cache-Control", "no-cache")
	w.Header().Set("Connection", "keep-alive")
	w.Header().Set("Access-Control-Allow-Origin", "*")

	// Create stream session
	sessionID := uuid.New().String()
	stream := s.streamManager.CreateSession(sessionID, w, r)

	// Process message with streaming
	go s.processMessageWithStreaming(r.Context(), &params, stream, request.ID)

	// Keep connection alive
	<-r.Context().Done()
	s.streamManager.CloseSession(sessionID)
}

// ============================================================================
// TASK MANAGEMENT HANDLERS
// ============================================================================

// handleTasksGet retrieves task status and results
func (s *A2AServer) handleTasksGet(w http.ResponseWriter, r *http.Request) {
	s.logger.Info("📋 Processing A2A tasks/get request")

	var request JSONRPCRequest
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		s.sendJSONRPCError(w, request.ID, -32700, "Parse error", nil)
		return
	}

	var params TaskQueryParams
	if err := s.parseParams(request.Params, &params); err != nil {
		s.sendJSONRPCError(w, request.ID, -32602, "Invalid params", err.Error())
		return
	}

	// Get task from manager
	task, err := s.taskManager.GetTask(params.ID)
	if err != nil {
		s.sendJSONRPCError(w, request.ID, ErrorTaskNotFound, "Task not found", nil)
		return
	}

	response := JSONRPCResponse{
		JSONRPC: "2.0",
		ID:      request.ID,
		Result:  task,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// handleTasksCancel cancels a running task
func (s *A2AServer) handleTasksCancel(w http.ResponseWriter, r *http.Request) {
	s.logger.Info("❌ Processing A2A tasks/cancel request")

	var request JSONRPCRequest
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		s.sendJSONRPCError(w, request.ID, -32700, "Parse error", nil)
		return
	}

	var params TaskIdParams
	if err := s.parseParams(request.Params, &params); err != nil {
		s.sendJSONRPCError(w, request.ID, -32602, "Invalid params", err.Error())
		return
	}

	// Cancel task
	if err := s.taskManager.CancelTask(params.ID); err != nil {
		if strings.Contains(err.Error(), "not found") {
			s.sendJSONRPCError(w, request.ID, ErrorTaskNotFound, "Task not found", nil)
		} else {
			s.sendJSONRPCError(w, request.ID, ErrorTaskNotCancelable, "Task cannot be canceled", nil)
		}
		return
	}

	response := JSONRPCResponse{
		JSONRPC: "2.0",
		ID:      request.ID,
		Result:  map[string]interface{}{"success": true},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// ============================================================================
// HELPER METHODS
// ============================================================================

// sendJSONRPCError sends a JSON-RPC error response
func (s *A2AServer) sendJSONRPCError(w http.ResponseWriter, id interface{}, code int, message string, data interface{}) {
	response := JSONRPCResponse{
		JSONRPC: "2.0",
		ID:      id,
		Error: &JSONRPCError{
			Code:    code,
			Message: message,
			Data:    data,
		},
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK) // JSON-RPC errors use 200 OK
	json.NewEncoder(w).Encode(response)
}

// parseParams parses JSON-RPC parameters into a struct
func (s *A2AServer) parseParams(params interface{}, target interface{}) error {
	if params == nil {
		return fmt.Errorf("missing parameters")
	}

	// Convert to JSON and back to parse into target struct
	jsonData, err := json.Marshal(params)
	if err != nil {
		return fmt.Errorf("failed to marshal params: %w", err)
	}

	if err := json.Unmarshal(jsonData, target); err != nil {
		return fmt.Errorf("failed to unmarshal params: %w", err)
	}

	return nil
}

// registerDefaultSkills registers the default HVAC skills
func (s *A2AServer) registerDefaultSkills() error {
	s.logger.Info("📚 Registering default HVAC skills")

	// This will be implemented when we create the skill registry
	// For now, just log that we're ready
	s.logger.Info("✅ Default skills registration ready")
	return nil
}

// processMessage handles the core message processing logic
func (s *A2AServer) processMessage(ctx context.Context, params *MessageSendParams) (interface{}, error) {
	s.logger.Info("🔄 Processing message through skill system")

	// Generate task ID if not provided
	taskID := params.TaskID
	if taskID == "" {
		taskID = uuid.New().String()
	}

	// Generate context ID if not provided
	contextID := params.ContextID
	if contextID == "" {
		contextID = uuid.New().String()
	}

	// Create task
	task := &Task{
		ID:        taskID,
		ContextID: contextID,
		Status: TaskStatus{
			State:     TaskStateSubmitted,
			Timestamp: time.Now().UTC().Format(time.RFC3339),
		},
		History: []Message{params.Message},
		Kind:    "task",
		Metadata: map[string]interface{}{
			"created_at": time.Now().UTC(),
			"source":     "a2a",
		},
	}

	// Add message IDs
	if params.Message.MessageID == "" {
		task.History[0].MessageID = uuid.New().String()
	}
	task.History[0].TaskID = taskID
	task.History[0].ContextID = contextID

	// Store task
	s.tasksMutex.Lock()
	s.activeTasks[taskID] = task
	s.tasksMutex.Unlock()

	// For now, return a simple completion
	// TODO: Integrate with skill processing
	task.Status.State = TaskStateCompleted
	task.Status.Timestamp = time.Now().UTC().Format(time.RFC3339)

	// Add a simple response artifact
	task.Artifacts = []Artifact{
		{
			ArtifactID: uuid.New().String(),
			Name:       "response",
			Parts: []Part{
				{
					Type: "text",
					Text: "Hello! I'm the HVAC CRM Intelligence Agent. I'm ready to help with your HVAC business needs.",
				},
			},
		},
	}

	return task, nil
}

// processMessageWithStreaming handles streaming message processing
func (s *A2AServer) processMessageWithStreaming(ctx context.Context, params *MessageSendParams, stream *StreamSession, requestID interface{}) {
	s.logger.Info("🌊 Processing streaming message")

	// Generate task ID
	taskID := uuid.New().String()
	contextID := params.ContextID
	if contextID == "" {
		contextID = uuid.New().String()
	}

	// Send initial task response
	initialResponse := &SendStreamingMessageResponse{
		ID:        taskID,
		ContextID: contextID,
		Status: &TaskStatus{
			State:     TaskStateSubmitted,
			Timestamp: time.Now().UTC().Format(time.RFC3339),
		},
		History: []Message{params.Message},
		Kind:    "task",
		Metadata: map[string]interface{}{
			"created_at": time.Now().UTC(),
			"streaming":  true,
		},
	}

	stream.SendEvent(requestID, initialResponse)

	// Simulate processing with status updates
	time.Sleep(1 * time.Second)

	// Send working status
	statusUpdate := &SendStreamingMessageResponse{
		TaskID:    taskID,
		ContextID: contextID,
		Status: &TaskStatus{
			State:     TaskStateWorking,
			Timestamp: time.Now().UTC().Format(time.RFC3339),
		},
		Kind: "status-update",
	}
	stream.SendEvent(requestID, statusUpdate)

	// Send artifact update
	time.Sleep(2 * time.Second)
	artifactUpdate := &SendStreamingMessageResponse{
		TaskID:    taskID,
		ContextID: contextID,
		Artifact: &Artifact{
			ArtifactID: uuid.New().String(),
			Name:       "streaming_response",
			Parts: []Part{
				{
					Type: "text",
					Text: "Processing your HVAC request with streaming updates...",
				},
			},
		},
		Append:    false,
		LastChunk: true,
		Kind:      "artifact-update",
	}
	stream.SendEvent(requestID, artifactUpdate)

	// Send final completion
	time.Sleep(1 * time.Second)
	finalUpdate := &SendStreamingMessageResponse{
		TaskID:    taskID,
		ContextID: contextID,
		Status: &TaskStatus{
			State:     TaskStateCompleted,
			Timestamp: time.Now().UTC().Format(time.RFC3339),
		},
		Final: true,
		Kind:  "status-update",
	}
	stream.SendEvent(requestID, finalUpdate)
}

// Placeholder handlers for remaining endpoints
func (s *A2AServer) handleTasksResubscribe(w http.ResponseWriter, r *http.Request) {
	s.logger.Info("🔄 Processing A2A tasks/resubscribe request")
	// TODO: Implement SSE resubscription
}

func (s *A2AServer) handlePushNotificationConfigSet(w http.ResponseWriter, r *http.Request) {
	s.logger.Info("🔔 Processing A2A push notification config set")
	// TODO: Implement push notification configuration
}

func (s *A2AServer) handlePushNotificationConfigGet(w http.ResponseWriter, r *http.Request) {
	s.logger.Info("🔔 Processing A2A push notification config get")
	// TODO: Implement push notification configuration retrieval
}

// ============================================================================
// PLACEHOLDER INTERFACES (to be implemented)
// ============================================================================

// SkillRegistry manages available A2A skills
type SkillRegistry struct {
	logger *log.Helper
}

func NewSkillRegistry(logger log.Logger) *SkillRegistry {
	return &SkillRegistry{logger: log.NewHelper(logger)}
}

// TaskManager handles task lifecycle
type TaskManager struct {
	logger *log.Helper
	tasks  map[string]*Task
	mutex  sync.RWMutex
}

func NewTaskManager(logger log.Logger) *TaskManager {
	return &TaskManager{
		logger: log.NewHelper(logger),
		tasks:  make(map[string]*Task),
	}
}

func (tm *TaskManager) GetTask(id string) (*Task, error) {
	tm.mutex.RLock()
	defer tm.mutex.RUnlock()

	task, exists := tm.tasks[id]
	if !exists {
		return nil, fmt.Errorf("task not found")
	}
	return task, nil
}

func (tm *TaskManager) CancelTask(id string) error {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	task, exists := tm.tasks[id]
	if !exists {
		return fmt.Errorf("task not found")
	}

	if task.Status.State == TaskStateCompleted || task.Status.State == TaskStateFailed {
		return fmt.Errorf("task cannot be canceled")
	}

	task.Status.State = TaskStateCanceled
	task.Status.Timestamp = time.Now().UTC().Format(time.RFC3339)
	return nil
}

// StreamManager handles SSE streaming sessions
type StreamManager struct {
	logger   *log.Helper
	sessions map[string]*StreamSession
	mutex    sync.RWMutex
}

func NewStreamManager(logger log.Logger) *StreamManager {
	return &StreamManager{
		logger:   log.NewHelper(logger),
		sessions: make(map[string]*StreamSession),
	}
}

func (sm *StreamManager) CreateSession(id string, w http.ResponseWriter, r *http.Request) *StreamSession {
	session := &StreamSession{
		ID:      id,
		Writer:  w,
		Request: r,
		Created: time.Now(),
	}

	sm.mutex.Lock()
	sm.sessions[id] = session
	sm.mutex.Unlock()

	return session
}

func (sm *StreamManager) CloseSession(id string) {
	sm.mutex.Lock()
	delete(sm.sessions, id)
	sm.mutex.Unlock()
}

// StreamSession represents an active SSE session
type StreamSession struct {
	ID      string
	Writer  http.ResponseWriter
	Request *http.Request
	Created time.Time
}

func (ss *StreamSession) SendEvent(id interface{}, data interface{}) error {
	response := JSONRPCResponse{
		JSONRPC: "2.0",
		ID:      id,
		Result:  data,
	}

	jsonData, err := json.Marshal(response)
	if err != nil {
		return err
	}

	_, err = fmt.Fprintf(ss.Writer, "data: %s\n\n", string(jsonData))
	if err != nil {
		return err
	}

	if flusher, ok := ss.Writer.(http.Flusher); ok {
		flusher.Flush()
	}

	return nil
}
