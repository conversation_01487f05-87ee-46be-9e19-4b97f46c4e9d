package a2a

import (
	"context"
)

// 🔌 A2A Interfaces - Bridge Interfaces for Integration
// Defines interfaces for MCP and LangChain integration without import cycles

// MCPBridge interface for MCP server communication
type MCPBridge interface {
	ExecuteSkill(ctx context.Context, skillName string, input map[string]interface{}) (interface{}, error)
	GetSkillMappings() map[string]string
	AddSkillMapping(skillName, mcpTool string)
	RemoveSkillMapping(skillName string)
}

// LangChainBridge interface for LangChain communication
type LangChainBridge interface {
	ProcessMessage(ctx context.Context, message *Message) (*Message, error)
	GetChainMappings() map[string]string
	AddChainMapping(skillName, chainName string)
	RemoveChainMapping(skillName string)
}

// SkillProcessor interface for skill execution
type SkillProcessor interface {
	ProcessSkill(ctx context.Context, skillName string, input map[string]interface{}) (interface{}, error)
	ListAvailableSkills() []string
	GetSkillInfo(skillName string) (*SkillInfo, error)
}

// SkillInfo contains information about a skill
type SkillInfo struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	InputSchema map[string]interface{} `json:"input_schema"`
	Category    string                 `json:"category"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// TaskProcessor interface for task management
type TaskProcessor interface {
	CreateTask(ctx context.Context, message *Message) (*Task, error)
	UpdateTask(ctx context.Context, taskID string, status TaskStatus) error
	GetTask(ctx context.Context, taskID string) (*Task, error)
	CancelTask(ctx context.Context, taskID string) error
	ListTasks(ctx context.Context, filters map[string]interface{}) ([]*Task, error)
}

// StreamProcessor interface for streaming operations
type StreamProcessor interface {
	CreateStream(ctx context.Context, sessionID string) (*StreamSession, error)
	SendStreamEvent(ctx context.Context, sessionID string, event interface{}) error
	CloseStream(ctx context.Context, sessionID string) error
	GetActiveStreams() []string
}

// NotificationProcessor interface for push notifications
type NotificationProcessor interface {
	SendNotification(ctx context.Context, config *PushNotificationConfig, payload interface{}) error
	ValidateNotificationConfig(config *PushNotificationConfig) error
	GetNotificationStatus(ctx context.Context, notificationID string) (*NotificationStatus, error)
}

// NotificationStatus represents the status of a push notification
type NotificationStatus struct {
	ID          string                 `json:"id"`
	Status      string                 `json:"status"` // "pending", "sent", "delivered", "failed"
	SentAt      string                 `json:"sent_at,omitempty"`
	DeliveredAt string                 `json:"delivered_at,omitempty"`
	Error       string                 `json:"error,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// AuthenticationProcessor interface for A2A authentication
type AuthenticationProcessor interface {
	ValidateRequest(ctx context.Context, request interface{}) (*AuthContext, error)
	GetUserPermissions(ctx context.Context, userID string) ([]string, error)
	CheckSkillPermission(ctx context.Context, userID, skillName string) bool
}

// AuthContext contains authentication information
type AuthContext struct {
	UserID      string                 `json:"user_id"`
	Permissions []string               `json:"permissions"`
	Metadata    map[string]interface{} `json:"metadata"`
	ExpiresAt   string                 `json:"expires_at"`
}

// MetricsCollector interface for A2A metrics
type MetricsCollector interface {
	RecordRequest(ctx context.Context, method string, duration int64, success bool)
	RecordSkillExecution(ctx context.Context, skillName string, duration int64, success bool)
	RecordTokenUsage(ctx context.Context, inputTokens, outputTokens int)
	GetMetrics(ctx context.Context, timeRange string) (*MetricsData, error)
}

// MetricsData contains A2A metrics
type MetricsData struct {
	RequestCount    int64                  `json:"request_count"`
	SuccessRate     float64                `json:"success_rate"`
	AverageLatency  float64                `json:"average_latency"`
	SkillUsage      map[string]int64       `json:"skill_usage"`
	TokenUsage      *TokenUsageMetrics     `json:"token_usage"`
	ErrorBreakdown  map[string]int64       `json:"error_breakdown"`
	Metadata        map[string]interface{} `json:"metadata"`
}

// TokenUsageMetrics tracks token consumption
type TokenUsageMetrics struct {
	TotalInputTokens  int64   `json:"total_input_tokens"`
	TotalOutputTokens int64   `json:"total_output_tokens"`
	AveragePerRequest float64 `json:"average_per_request"`
	CostEstimate      float64 `json:"cost_estimate"`
}

// ConfigurationManager interface for A2A configuration
type ConfigurationManager interface {
	GetConfiguration(ctx context.Context, key string) (interface{}, error)
	SetConfiguration(ctx context.Context, key string, value interface{}) error
	ListConfigurations(ctx context.Context) (map[string]interface{}, error)
	ValidateConfiguration(ctx context.Context, config map[string]interface{}) error
}

// HealthChecker interface for A2A health monitoring
type HealthChecker interface {
	CheckHealth(ctx context.Context) (*HealthStatus, error)
	CheckDependencies(ctx context.Context) (map[string]*DependencyStatus, error)
	GetSystemInfo(ctx context.Context) (*SystemInfo, error)
}

// HealthStatus represents the health of the A2A system
type HealthStatus struct {
	Status      string                 `json:"status"` // "healthy", "degraded", "unhealthy"
	Timestamp   string                 `json:"timestamp"`
	Version     string                 `json:"version"`
	Uptime      int64                  `json:"uptime"`
	Checks      map[string]bool        `json:"checks"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// DependencyStatus represents the status of a dependency
type DependencyStatus struct {
	Name      string                 `json:"name"`
	Status    string                 `json:"status"`
	Latency   int64                  `json:"latency"`
	Error     string                 `json:"error,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// SystemInfo contains system information
type SystemInfo struct {
	Version       string                 `json:"version"`
	Environment   string                 `json:"environment"`
	StartTime     string                 `json:"start_time"`
	ActiveTasks   int                    `json:"active_tasks"`
	ActiveStreams int                    `json:"active_streams"`
	MemoryUsage   *MemoryInfo           `json:"memory_usage"`
	Metadata      map[string]interface{} `json:"metadata"`
}

// MemoryInfo contains memory usage information
type MemoryInfo struct {
	Allocated     uint64  `json:"allocated"`
	TotalAlloc    uint64  `json:"total_alloc"`
	Sys           uint64  `json:"sys"`
	NumGC         uint32  `json:"num_gc"`
	GCCPUFraction float64 `json:"gc_cpu_fraction"`
}
