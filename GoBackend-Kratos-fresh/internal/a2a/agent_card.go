package a2a

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

// 🎯 A2A Agent Card Generator - HVAC CRM Intelligence Agent Discovery
// Generates comprehensive agent cards for Google A2A protocol compliance

type AgentCardGenerator struct {
	baseURL     string
	version     string
	environment string
	logger      *log.Helper
}

// NewAgentCardGenerator creates a new agent card generator
func NewAgentCardGenerator(baseURL, version, environment string, logger log.Logger) *AgentCardGenerator {
	return &AgentCardGenerator{
		baseURL:     baseURL,
		version:     version,
		environment: environment,
		logger:      log.NewHelper(logger),
	}
}

// GeneratePublicAgentCard creates the public agent card for discovery
func (g *AgentCardGenerator) GeneratePublicAgentCard() (*AgentCard, error) {
	g.logger.Info("🎯 Generating public A2A agent card for HVAC CRM")

	card := &AgentCard{
		Name:        "HVAC CRM Intelligence Agent",
		Description: "Comprehensive HVAC business management with AI-powered insights, customer lifecycle management, and equipment optimization",
		Version:     g.version,
		URL:         fmt.Sprintf("%s/a2a", g.baseURL),
		Provider: &AgentProvider{
			Name:        "Koldbringers HVAC Solutions",
			URL:         "https://koldbringers.pl",
			Description: "Advanced HVAC CRM and business intelligence platform",
		},
		Capabilities: AgentCapabilities{
			Streaming:         true,
			PushNotifications: true,
			MultiTurn:         true,
		},
		Skills:                           g.generatePublicSkills(),
		SecuritySchemes:                  g.generateSecuritySchemes(),
		Security:                         g.generateSecurityRequirements(),
		SupportsAuthenticatedExtendedCard: true,
		Metadata: map[string]interface{}{
			"industry":     "HVAC",
			"region":       "Poland",
			"languages":    []string{"pl", "en"},
			"timezone":     "Europe/Warsaw",
			"environment":  g.environment,
			"generated_at": time.Now().UTC().Format(time.RFC3339),
			"features": []string{
				"customer_management",
				"equipment_tracking",
				"predictive_maintenance",
				"financial_analytics",
				"workflow_automation",
				"ai_diagnostics",
			},
		},
	}

	g.logger.Infof("✅ Generated public agent card with %d skills", len(card.Skills))
	return card, nil
}

// GenerateAuthenticatedExtendedCard creates the extended card with full capabilities
func (g *AgentCardGenerator) GenerateAuthenticatedExtendedCard() (*AgentCard, error) {
	g.logger.Info("🔐 Generating authenticated extended A2A agent card")

	// Start with public card
	card, err := g.GeneratePublicAgentCard()
	if err != nil {
		return nil, fmt.Errorf("failed to generate base card: %w", err)
	}

	// Add extended skills for authenticated users
	extendedSkills := g.generateExtendedSkills()
	card.Skills = append(card.Skills, extendedSkills...)

	// Add extended metadata
	if card.Metadata == nil {
		card.Metadata = make(map[string]interface{})
	}
	
	card.Metadata["extended"] = true
	card.Metadata["advanced_features"] = []string{
		"financial_management",
		"advanced_analytics",
		"custom_workflows",
		"api_integrations",
		"admin_operations",
		"data_export",
	}

	g.logger.Infof("✅ Generated extended agent card with %d total skills", len(card.Skills))
	return card, nil
}

// generatePublicSkills creates the public skill set
func (g *AgentCardGenerator) generatePublicSkills() []AgentSkill {
	return []AgentSkill{
		{
			Name:        "hvac_diagnostics",
			Description: "AI-powered HVAC system diagnostics and troubleshooting recommendations",
			InputSchema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"system_type":    map[string]interface{}{"type": "string", "enum": []string{"heating", "cooling", "ventilation", "combined"}},
					"symptoms":       map[string]interface{}{"type": "array", "items": map[string]interface{}{"type": "string"}},
					"equipment_age":  map[string]interface{}{"type": "number"},
					"maintenance_history": map[string]interface{}{"type": "array"},
				},
				"required": []string{"system_type", "symptoms"},
			},
			Metadata: map[string]interface{}{
				"category": "diagnostics",
				"ai_powered": true,
			},
		},
		{
			Name:        "customer_inquiry",
			Description: "Handle customer inquiries, scheduling, and basic support requests",
			InputSchema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"inquiry_type": map[string]interface{}{"type": "string", "enum": []string{"service", "quote", "emergency", "general"}},
					"customer_info": map[string]interface{}{"type": "object"},
					"message": map[string]interface{}{"type": "string"},
				},
				"required": []string{"inquiry_type", "message"},
			},
			Metadata: map[string]interface{}{
				"category": "customer_service",
				"response_time": "immediate",
			},
		},
		{
			Name:        "equipment_status",
			Description: "Check equipment status, performance metrics, and maintenance schedules",
			InputSchema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"equipment_id": map[string]interface{}{"type": "string"},
					"customer_id":  map[string]interface{}{"type": "string"},
					"check_type":   map[string]interface{}{"type": "string", "enum": []string{"status", "performance", "maintenance", "all"}},
				},
				"required": []string{"equipment_id"},
			},
			Metadata: map[string]interface{}{
				"category": "equipment",
				"real_time": true,
			},
		},
		{
			Name:        "maintenance_planning",
			Description: "Generate maintenance schedules and preventive care recommendations",
			InputSchema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"equipment_list": map[string]interface{}{"type": "array"},
					"time_horizon":   map[string]interface{}{"type": "string", "enum": []string{"1_month", "3_months", "6_months", "1_year"}},
					"priority_level": map[string]interface{}{"type": "string", "enum": []string{"critical", "high", "medium", "low"}},
				},
				"required": []string{"equipment_list", "time_horizon"},
			},
			Metadata: map[string]interface{}{
				"category": "maintenance",
				"ai_powered": true,
			},
		},
		{
			Name:        "quote_generation",
			Description: "Generate service quotes and cost estimates for HVAC work",
			InputSchema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"service_type": map[string]interface{}{"type": "string"},
					"equipment_details": map[string]interface{}{"type": "object"},
					"customer_location": map[string]interface{}{"type": "object"},
					"urgency": map[string]interface{}{"type": "string", "enum": []string{"emergency", "urgent", "standard", "scheduled"}},
				},
				"required": []string{"service_type"},
			},
			Metadata: map[string]interface{}{
				"category": "sales",
				"pricing_model": "dynamic",
			},
		},
		{
			Name:        "scheduling_assistance",
			Description: "Help with appointment scheduling and technician dispatch",
			InputSchema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"service_type": map[string]interface{}{"type": "string"},
					"preferred_date": map[string]interface{}{"type": "string", "format": "date"},
					"time_preference": map[string]interface{}{"type": "string"},
					"customer_id": map[string]interface{}{"type": "string"},
				},
				"required": []string{"service_type"},
			},
			Metadata: map[string]interface{}{
				"category": "scheduling",
				"real_time": true,
			},
		},
	}
}

// generateExtendedSkills creates additional skills for authenticated users
func (g *AgentCardGenerator) generateExtendedSkills() []AgentSkill {
	return []AgentSkill{
		{
			Name:        "financial_analytics",
			Description: "Advanced financial reporting, revenue analysis, and business intelligence",
			InputSchema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"report_type": map[string]interface{}{"type": "string", "enum": []string{"revenue", "profit", "customer_value", "forecasting"}},
					"time_period": map[string]interface{}{"type": "object"},
					"filters": map[string]interface{}{"type": "object"},
				},
				"required": []string{"report_type"},
			},
			Metadata: map[string]interface{}{
				"category": "analytics",
				"auth_required": true,
				"role_required": "manager",
			},
		},
		{
			Name:        "workflow_automation",
			Description: "Create and manage automated business workflows and processes",
			InputSchema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"workflow_type": map[string]interface{}{"type": "string"},
					"triggers": map[string]interface{}{"type": "array"},
					"actions": map[string]interface{}{"type": "array"},
				},
				"required": []string{"workflow_type"},
			},
			Metadata: map[string]interface{}{
				"category": "automation",
				"auth_required": true,
			},
		},
		{
			Name:        "data_export",
			Description: "Export business data in various formats for analysis and reporting",
			InputSchema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"data_type": map[string]interface{}{"type": "string"},
					"format": map[string]interface{}{"type": "string", "enum": []string{"csv", "xlsx", "json", "pdf"}},
					"filters": map[string]interface{}{"type": "object"},
				},
				"required": []string{"data_type", "format"},
			},
			Metadata: map[string]interface{}{
				"category": "data",
				"auth_required": true,
				"role_required": "admin",
			},
		},
	}
}

// generateSecuritySchemes defines authentication methods
func (g *AgentCardGenerator) generateSecuritySchemes() map[string]SecurityScheme {
	return map[string]SecurityScheme{
		"bearerAuth": {
			Type:         "http",
			Scheme:       "bearer",
			BearerFormat: "JWT",
			Description:  "JWT Bearer token authentication",
		},
		"apiKey": {
			Type:        "apiKey",
			Description: "API key authentication via header",
		},
	}
}

// generateSecurityRequirements defines security requirements
func (g *AgentCardGenerator) generateSecurityRequirements() []map[string][]string {
	return []map[string][]string{
		{
			"bearerAuth": []string{},
		},
		{
			"apiKey": []string{},
		},
	}
}

// ToJSON converts agent card to JSON
func (g *AgentCardGenerator) ToJSON(card *AgentCard) ([]byte, error) {
	return json.MarshalIndent(card, "", "  ")
}

// ValidateAgentCard validates the agent card structure
func (g *AgentCardGenerator) ValidateAgentCard(card *AgentCard) error {
	if card.Name == "" {
		return fmt.Errorf("agent name is required")
	}
	if card.Description == "" {
		return fmt.Errorf("agent description is required")
	}
	if card.Version == "" {
		return fmt.Errorf("agent version is required")
	}
	if card.URL == "" {
		return fmt.Errorf("agent URL is required")
	}
	if len(card.Skills) == 0 {
		return fmt.Errorf("at least one skill is required")
	}

	g.logger.Info("✅ Agent card validation passed")
	return nil
}
