package pipeline

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"gobackend-hvac-kratos/internal/a2a"
	"gobackend-hvac-kratos/internal/a2a/database"
	"gobackend-hvac-kratos/internal/a2a/skills"
	"gobackend-hvac-kratos/internal/email"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/uuid"
)

// 📧 Email-to-A2A Pipeline - Automated Email Processing with A2A Skills
// Connects email intelligence with A2A agent capabilities

type EmailA2APipeline struct {
	logger           *log.Helper
	a2aRepo          *database.A2ARepository
	skillProcessor   *skills.HVACSkillProcessor
	emailService     *email.EmailIntelligenceService
	a2aServer        a2a.A2AServer
}

// EmailProcessingResult represents the result of email processing
type EmailProcessingResult struct {
	EmailID           string                 `json:"email_id"`
	ConversationID    string                 `json:"conversation_id"`
	TaskID            string                 `json:"task_id"`
	Intent            string                 `json:"intent"`
	ConfidenceScore   float64                `json:"confidence_score"`
	SkillsTriggered   []string               `json:"skills_triggered"`
	AutomatedResponse bool                   `json:"automated_response"`
	ProcessingTime    time.Duration          `json:"processing_time"`
	Metadata          map[string]interface{} `json:"metadata"`
}

// NewEmailA2APipeline creates a new email-to-A2A pipeline
func NewEmailA2APipeline(
	a2aRepo *database.A2ARepository,
	skillProcessor *skills.HVACSkillProcessor,
	emailService *email.EmailIntelligenceService,
	logger log.Logger,
) *EmailA2APipeline {
	logHelper := log.NewHelper(logger)
	logHelper.Info("📧 Initializing Email-to-A2A Pipeline")

	return &EmailA2APipeline{
		logger:         logHelper,
		a2aRepo:        a2aRepo,
		skillProcessor: skillProcessor,
		emailService:   emailService,
	}
}

// ProcessIncomingEmail processes an incoming email through A2A pipeline
func (p *EmailA2APipeline) ProcessIncomingEmail(ctx context.Context, emailData *EmailData) (*EmailProcessingResult, error) {
	p.logger.Infof("📨 Processing incoming email: %s", emailData.Subject)
	startTime := time.Now()

	// Step 1: Extract email intelligence
	intelligence, err := p.extractEmailIntelligence(ctx, emailData)
	if err != nil {
		return nil, fmt.Errorf("failed to extract email intelligence: %w", err)
	}

	// Step 2: Classify intent and determine A2A skills
	intent := p.classifyEmailIntent(intelligence)
	skills := p.determineRequiredSkills(intent, intelligence)
	
	// Step 3: Create or get conversation context
	conversationID := p.getOrCreateConversationID(ctx, emailData, intelligence)
	
	// Step 4: Process through A2A skills
	results := make(map[string]interface{})
	for _, skillName := range skills {
		skillInput := p.prepareSkillInput(skillName, intelligence, emailData)
		skillResult, err := p.skillProcessor.ProcessSkill(ctx, skillName, skillInput)
		if err != nil {
			p.logger.Errorf("Skill %s failed: %v", skillName, err)
			continue
		}
		results[skillName] = skillResult
	}

	// Step 5: Generate automated response if appropriate
	automatedResponse := p.shouldGenerateAutomatedResponse(intent, intelligence)
	if automatedResponse {
		if err := p.generateAndSendResponse(ctx, emailData, intelligence, results); err != nil {
			p.logger.Errorf("Failed to send automated response: %v", err)
		}
	}

	// Step 6: Record processing results
	processingResult := &EmailProcessingResult{
		EmailID:           emailData.ID,
		ConversationID:    conversationID,
		Intent:            intent,
		ConfidenceScore:   intelligence.ConfidenceScore,
		SkillsTriggered:   skills,
		AutomatedResponse: automatedResponse,
		ProcessingTime:    time.Since(startTime),
		Metadata: map[string]interface{}{
			"email_from":    emailData.From,
			"email_subject": emailData.Subject,
			"skills_results": results,
			"intelligence":   intelligence,
		},
	}

	// Save to database
	if err := p.saveProcessingResult(ctx, processingResult); err != nil {
		p.logger.Errorf("Failed to save processing result: %v", err)
	}

	p.logger.Infof("✅ Email processed successfully in %v", processingResult.ProcessingTime)
	return processingResult, nil
}

// ============================================================================
// EMAIL INTELLIGENCE EXTRACTION
// ============================================================================

// EmailData represents incoming email data
type EmailData struct {
	ID          string                 `json:"id"`
	From        string                 `json:"from"`
	To          string                 `json:"to"`
	Subject     string                 `json:"subject"`
	Body        string                 `json:"body"`
	Attachments []EmailAttachment      `json:"attachments"`
	ReceivedAt  time.Time              `json:"received_at"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// EmailAttachment represents an email attachment
type EmailAttachment struct {
	Filename    string `json:"filename"`
	ContentType string `json:"content_type"`
	Size        int64  `json:"size"`
	Data        []byte `json:"data,omitempty"`
}

// EmailIntelligence represents extracted intelligence from email
type EmailIntelligence struct {
	Intent          string                 `json:"intent"`
	Entities        map[string]interface{} `json:"entities"`
	Sentiment       string                 `json:"sentiment"`
	Priority        string                 `json:"priority"`
	CustomerInfo    *CustomerInfo          `json:"customer_info,omitempty"`
	EquipmentInfo   *EquipmentInfo         `json:"equipment_info,omitempty"`
	ServiceRequest  *ServiceRequest        `json:"service_request,omitempty"`
	ConfidenceScore float64                `json:"confidence_score"`
	Keywords        []string               `json:"keywords"`
}

// CustomerInfo represents extracted customer information
type CustomerInfo struct {
	Name    string `json:"name,omitempty"`
	Email   string `json:"email,omitempty"`
	Phone   string `json:"phone,omitempty"`
	Address string `json:"address,omitempty"`
	ID      string `json:"id,omitempty"`
}

// EquipmentInfo represents extracted equipment information
type EquipmentInfo struct {
	Type         string `json:"type,omitempty"`
	Model        string `json:"model,omitempty"`
	SerialNumber string `json:"serial_number,omitempty"`
	Age          string `json:"age,omitempty"`
	Issues       []string `json:"issues,omitempty"`
}

// ServiceRequest represents extracted service request information
type ServiceRequest struct {
	Type        string    `json:"type"`
	Urgency     string    `json:"urgency"`
	Description string    `json:"description"`
	RequestedDate *time.Time `json:"requested_date,omitempty"`
	Location    string    `json:"location,omitempty"`
}

func (p *EmailA2APipeline) extractEmailIntelligence(ctx context.Context, emailData *EmailData) (*EmailIntelligence, error) {
	p.logger.Info("🧠 Extracting email intelligence")

	// Use email intelligence service to analyze content
	// This is a simplified implementation - in reality, you'd use your email service
	
	intelligence := &EmailIntelligence{
		Intent:          p.extractIntent(emailData),
		Entities:        p.extractEntities(emailData),
		Sentiment:       p.analyzeSentiment(emailData),
		Priority:        p.assessPriority(emailData),
		CustomerInfo:    p.extractCustomerInfo(emailData),
		EquipmentInfo:   p.extractEquipmentInfo(emailData),
		ServiceRequest:  p.extractServiceRequest(emailData),
		ConfidenceScore: 0.85, // Placeholder
		Keywords:        p.extractKeywords(emailData),
	}

	return intelligence, nil
}

// ============================================================================
// INTENT CLASSIFICATION AND SKILL MAPPING
// ============================================================================

func (p *EmailA2APipeline) classifyEmailIntent(intelligence *EmailIntelligence) string {
	// Primary intent classification
	if intelligence.Intent != "" {
		return intelligence.Intent
	}

	// Fallback classification based on keywords and content
	keywords := strings.Join(intelligence.Keywords, " ")
	keywords = strings.ToLower(keywords)

	if strings.Contains(keywords, "repair") || strings.Contains(keywords, "broken") || strings.Contains(keywords, "not working") {
		return "repair_request"
	}
	if strings.Contains(keywords, "quote") || strings.Contains(keywords, "estimate") || strings.Contains(keywords, "price") {
		return "quote_request"
	}
	if strings.Contains(keywords, "schedule") || strings.Contains(keywords, "appointment") || strings.Contains(keywords, "book") {
		return "scheduling_request"
	}
	if strings.Contains(keywords, "maintenance") || strings.Contains(keywords, "service") || strings.Contains(keywords, "check") {
		return "maintenance_request"
	}
	if strings.Contains(keywords, "emergency") || strings.Contains(keywords, "urgent") {
		return "emergency_request"
	}

	return "general_inquiry"
}

func (p *EmailA2APipeline) determineRequiredSkills(intent string, intelligence *EmailIntelligence) []string {
	skills := []string{}

	switch intent {
	case "repair_request", "emergency_request":
		skills = append(skills, "hvac_diagnostics", "customer_inquiry", "scheduling_assistance")
	case "quote_request":
		skills = append(skills, "quote_generation", "customer_inquiry")
	case "scheduling_request":
		skills = append(skills, "scheduling_assistance", "customer_inquiry")
	case "maintenance_request":
		skills = append(skills, "maintenance_planning", "equipment_status", "customer_inquiry")
	default:
		skills = append(skills, "customer_inquiry")
	}

	// Add equipment status if equipment info is available
	if intelligence.EquipmentInfo != nil && intelligence.EquipmentInfo.Type != "" {
		if !contains(skills, "equipment_status") {
			skills = append(skills, "equipment_status")
		}
	}

	return skills
}

// ============================================================================
// SKILL INPUT PREPARATION
// ============================================================================

func (p *EmailA2APipeline) prepareSkillInput(skillName string, intelligence *EmailIntelligence, emailData *EmailData) map[string]interface{} {
	baseInput := map[string]interface{}{
		"email_id":     emailData.ID,
		"email_from":   emailData.From,
		"email_subject": emailData.Subject,
		"intent":       intelligence.Intent,
		"sentiment":    intelligence.Sentiment,
		"priority":     intelligence.Priority,
	}

	// Add customer info if available
	if intelligence.CustomerInfo != nil {
		baseInput["customer_id"] = intelligence.CustomerInfo.ID
		baseInput["customer_email"] = intelligence.CustomerInfo.Email
		baseInput["customer_name"] = intelligence.CustomerInfo.Name
	}

	// Add equipment info if available
	if intelligence.EquipmentInfo != nil {
		baseInput["equipment_type"] = intelligence.EquipmentInfo.Type
		baseInput["equipment_model"] = intelligence.EquipmentInfo.Model
		baseInput["equipment_issues"] = intelligence.EquipmentInfo.Issues
	}

	// Skill-specific input preparation
	switch skillName {
	case "hvac_diagnostics":
		if intelligence.EquipmentInfo != nil {
			baseInput["system_type"] = intelligence.EquipmentInfo.Type
			baseInput["symptoms"] = intelligence.EquipmentInfo.Issues
		}
	case "customer_inquiry":
		baseInput["inquiry_type"] = mapIntentToInquiryType(intelligence.Intent)
		baseInput["message"] = emailData.Body
	case "quote_generation":
		if intelligence.ServiceRequest != nil {
			baseInput["service_type"] = intelligence.ServiceRequest.Type
			baseInput["urgency"] = intelligence.ServiceRequest.Urgency
			baseInput["description"] = intelligence.ServiceRequest.Description
		}
	case "scheduling_assistance":
		if intelligence.ServiceRequest != nil {
			baseInput["service_type"] = intelligence.ServiceRequest.Type
			if intelligence.ServiceRequest.RequestedDate != nil {
				baseInput["preferred_date"] = intelligence.ServiceRequest.RequestedDate.Format("2006-01-02")
			}
		}
	}

	return baseInput
}

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

func (p *EmailA2APipeline) getOrCreateConversationID(ctx context.Context, emailData *EmailData, intelligence *EmailIntelligence) string {
	// Try to find existing conversation based on customer email
	// For now, create a new conversation ID
	return uuid.New().String()
}

func (p *EmailA2APipeline) shouldGenerateAutomatedResponse(intent string, intelligence *EmailIntelligence) bool {
	// Determine if we should send an automated response
	automatedIntents := []string{
		"general_inquiry",
		"quote_request",
		"scheduling_request",
		"maintenance_request",
	}

	return contains(automatedIntents, intent) && intelligence.ConfidenceScore > 0.7
}

func (p *EmailA2APipeline) generateAndSendResponse(ctx context.Context, emailData *EmailData, intelligence *EmailIntelligence, results map[string]interface{}) error {
	p.logger.Info("📤 Generating automated email response")

	// Generate response based on intent and A2A results
	response := p.generateResponseContent(intelligence.Intent, results)
	
	// Send response (implementation depends on your email service)
	p.logger.Infof("Would send response to %s: %s", emailData.From, response)
	
	return nil
}

func (p *EmailA2APipeline) saveProcessingResult(ctx context.Context, result *EmailProcessingResult) error {
	// Save processing result to database
	// Implementation depends on your database schema
	p.logger.Infof("💾 Saving processing result for email %s", result.EmailID)
	return nil
}

// Placeholder implementations for intelligence extraction
func (p *EmailA2APipeline) extractIntent(emailData *EmailData) string {
	// Implement intent extraction logic
	return ""
}

func (p *EmailA2APipeline) extractEntities(emailData *EmailData) map[string]interface{} {
	return make(map[string]interface{})
}

func (p *EmailA2APipeline) analyzeSentiment(emailData *EmailData) string {
	return "neutral"
}

func (p *EmailA2APipeline) assessPriority(emailData *EmailData) string {
	if strings.Contains(strings.ToLower(emailData.Subject), "urgent") || 
	   strings.Contains(strings.ToLower(emailData.Subject), "emergency") {
		return "high"
	}
	return "medium"
}

func (p *EmailA2APipeline) extractCustomerInfo(emailData *EmailData) *CustomerInfo {
	return &CustomerInfo{
		Email: emailData.From,
	}
}

func (p *EmailA2APipeline) extractEquipmentInfo(emailData *EmailData) *EquipmentInfo {
	return nil // Placeholder
}

func (p *EmailA2APipeline) extractServiceRequest(emailData *EmailData) *ServiceRequest {
	return nil // Placeholder
}

func (p *EmailA2APipeline) extractKeywords(emailData *EmailData) []string {
	// Simple keyword extraction
	text := emailData.Subject + " " + emailData.Body
	words := strings.Fields(strings.ToLower(text))
	
	hvacKeywords := []string{"hvac", "heating", "cooling", "air", "conditioning", "repair", "maintenance", "service"}
	var keywords []string
	
	for _, word := range words {
		for _, keyword := range hvacKeywords {
			if strings.Contains(word, keyword) {
				keywords = append(keywords, word)
			}
		}
	}
	
	return keywords
}

func (p *EmailA2APipeline) generateResponseContent(intent string, results map[string]interface{}) string {
	switch intent {
	case "quote_request":
		return "Thank you for your quote request. We've received your inquiry and will prepare a detailed estimate for you within 24 hours."
	case "scheduling_request":
		return "Thank you for contacting us. We'll check our technician availability and get back to you with appointment options shortly."
	case "maintenance_request":
		return "Thank you for your maintenance request. We've scheduled a system evaluation and will contact you to confirm the appointment."
	default:
		return "Thank you for contacting us. We've received your message and will respond within 24 hours."
	}
}

func mapIntentToInquiryType(intent string) string {
	switch intent {
	case "repair_request", "emergency_request":
		return "service"
	case "quote_request":
		return "quote"
	case "scheduling_request":
		return "scheduling"
	default:
		return "general"
	}
}

func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
