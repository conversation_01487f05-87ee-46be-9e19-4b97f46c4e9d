package a2a

import (
	"time"
)

// 🌐 A2A Protocol Types - Google Agent-to-Agent Protocol Implementation
// Comprehensive implementation of A2A v0.2.1 specification for HVAC CRM

// ============================================================================
// AGENT CARD STRUCTURES
// ============================================================================

// AgentCard represents the public agent discovery document
type AgentCard struct {
	Name                              string                    `json:"name"`
	Description                       string                    `json:"description"`
	Version                           string                    `json:"version"`
	URL                               string                    `json:"url"`
	Provider                          *AgentProvider            `json:"provider,omitempty"`
	Capabilities                      AgentCapabilities         `json:"capabilities"`
	Skills                            []AgentSkill              `json:"skills"`
	SecuritySchemes                   map[string]SecurityScheme `json:"securitySchemes,omitempty"`
	Security                          []map[string][]string     `json:"security,omitempty"`
	SupportsAuthenticatedExtendedCard bool                      `json:"supportsAuthenticatedExtendedCard,omitempty"`
	Metadata                          map[string]interface{}    `json:"metadata,omitempty"`
}

// AgentProvider contains information about the agent provider
type AgentProvider struct {
	Name        string `json:"name"`
	URL         string `json:"url,omitempty"`
	Description string `json:"description,omitempty"`
}

// AgentCapabilities defines what the agent can do
type AgentCapabilities struct {
	Streaming         bool `json:"streaming"`
	PushNotifications bool `json:"pushNotifications"`
	MultiTurn         bool `json:"multiTurn"`
}

// SecurityScheme defines authentication methods
type SecurityScheme struct {
	Type         string `json:"type"`
	Scheme       string `json:"scheme,omitempty"`
	BearerFormat string `json:"bearerFormat,omitempty"`
	Description  string `json:"description,omitempty"`
}

// AgentSkill represents a capability the agent can perform
type AgentSkill struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	InputSchema map[string]interface{} `json:"inputSchema,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// ============================================================================
// TASK MANAGEMENT STRUCTURES
// ============================================================================

// Task represents a unit of work managed by A2A
type Task struct {
	ID        string                 `json:"id"`
	ContextID string                 `json:"contextId,omitempty"`
	Status    TaskStatus             `json:"status"`
	Artifacts []Artifact             `json:"artifacts,omitempty"`
	History   []Message              `json:"history,omitempty"`
	Kind      string                 `json:"kind"` // "task"
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// TaskStatus represents the current state of a task
type TaskStatus struct {
	State     TaskState `json:"state"`
	Message   *Message  `json:"message,omitempty"`
	Timestamp string    `json:"timestamp,omitempty"`
}

// TaskState represents possible task states
type TaskState string

const (
	TaskStateSubmitted     TaskState = "submitted"
	TaskStateWorking       TaskState = "working"
	TaskStateInputRequired TaskState = "input-required"
	TaskStateCompleted     TaskState = "completed"
	TaskStateFailed        TaskState = "failed"
	TaskStateCanceled      TaskState = "canceled"
)

// ============================================================================
// MESSAGE STRUCTURES
// ============================================================================

// Message represents a communication turn between client and agent
type Message struct {
	Role      string                 `json:"role"` // "user" or "agent"
	Parts     []Part                 `json:"parts"`
	MessageID string                 `json:"messageId,omitempty"`
	TaskID    string                 `json:"taskId,omitempty"`
	ContextID string                 `json:"contextId,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// Part represents content within a message or artifact
type Part struct {
	Type     string                 `json:"type"` // "text", "file", "data"
	Text     string                 `json:"text,omitempty"`
	File     *File                  `json:"file,omitempty"`
	Data     interface{}            `json:"data,omitempty"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// File represents file content in messages or artifacts
type File struct {
	Name     string `json:"name,omitempty"`
	MimeType string `json:"mimeType"`
	Bytes    string `json:"bytes,omitempty"` // Base64 encoded
	URI      string `json:"uri,omitempty"`   // Alternative to bytes
}

// Artifact represents output generated by the agent
type Artifact struct {
	ArtifactID string                 `json:"artifactId"`
	Name       string                 `json:"name,omitempty"`
	Parts      []Part                 `json:"parts"`
	Metadata   map[string]interface{} `json:"metadata,omitempty"`
}

// ============================================================================
// JSON-RPC STRUCTURES
// ============================================================================

// JSONRPCRequest represents a JSON-RPC 2.0 request
type JSONRPCRequest struct {
	JSONRPC string      `json:"jsonrpc"`
	ID      interface{} `json:"id"`
	Method  string      `json:"method"`
	Params  interface{} `json:"params,omitempty"`
}

// JSONRPCResponse represents a JSON-RPC 2.0 response
type JSONRPCResponse struct {
	JSONRPC string        `json:"jsonrpc"`
	ID      interface{}   `json:"id"`
	Result  interface{}   `json:"result,omitempty"`
	Error   *JSONRPCError `json:"error,omitempty"`
}

// JSONRPCError represents a JSON-RPC 2.0 error
type JSONRPCError struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// ============================================================================
// A2A-SPECIFIC ERROR CODES
// ============================================================================

const (
	ErrorTaskNotFound                 = -32001
	ErrorTaskNotCancelable            = -32002
	ErrorPushNotificationNotSupported = -32003
	ErrorUnsupportedOperation         = -32004
	ErrorContentTypeNotSupported      = -32005
	ErrorInvalidAgentResponse         = -32006
)

// ============================================================================
// REQUEST/RESPONSE PARAMETERS
// ============================================================================

// MessageSendParams parameters for message/send method
type MessageSendParams struct {
	Message       Message                `json:"message"`
	ContextID     string                 `json:"contextId,omitempty"`
	TaskID        string                 `json:"taskId,omitempty"`
	Configuration *MessageConfiguration  `json:"configuration,omitempty"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
}

// MessageConfiguration for message sending options
type MessageConfiguration struct {
	Blocking               bool                    `json:"blocking,omitempty"`
	PushNotificationConfig *PushNotificationConfig `json:"pushNotificationConfig,omitempty"`
}

// PushNotificationConfig for webhook notifications
type PushNotificationConfig struct {
	URL            string                              `json:"url"`
	Token          string                              `json:"token,omitempty"`
	Authentication *PushNotificationAuthenticationInfo `json:"authentication,omitempty"`
}

// PushNotificationAuthenticationInfo for webhook auth
type PushNotificationAuthenticationInfo struct {
	Schemes     []string               `json:"schemes"`
	Credentials map[string]interface{} `json:"credentials,omitempty"`
}

// TaskQueryParams parameters for tasks/get method
type TaskQueryParams struct {
	ID string `json:"id"`
}

// TaskIdParams parameters for task operations
type TaskIdParams struct {
	ID string `json:"id"`
}

// ============================================================================
// STREAMING STRUCTURES
// ============================================================================

// SendStreamingMessageResponse for SSE streaming responses
type SendStreamingMessageResponse struct {
	TaskID    string      `json:"taskId,omitempty"`
	ContextID string      `json:"contextId,omitempty"`
	Status    *TaskStatus `json:"status,omitempty"`
	Artifact  *Artifact   `json:"artifact,omitempty"`
	Append    bool        `json:"append,omitempty"`
	LastChunk bool        `json:"lastChunk,omitempty"`
	Final     bool        `json:"final,omitempty"`
	Kind      string      `json:"kind"` // "status-update", "artifact-update", "task"

	// For initial task response
	ID        string                 `json:"id,omitempty"`
	Artifacts []Artifact             `json:"artifacts,omitempty"`
	History   []Message              `json:"history,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// MessageResponse for direct message responses (non-task)
type MessageResponse struct {
	MessageID string                 `json:"messageId"`
	ContextID string                 `json:"contextId,omitempty"`
	Parts     []Part                 `json:"parts"`
	Kind      string                 `json:"kind"` // "message"
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// ============================================================================
// HVAC-SPECIFIC EXTENSIONS
// ============================================================================

// HVACTaskMetadata contains HVAC-specific task information
type HVACTaskMetadata struct {
	CustomerID     string    `json:"customerId,omitempty"`
	JobID          string    `json:"jobId,omitempty"`
	EquipmentID    string    `json:"equipmentId,omitempty"`
	ServiceType    string    `json:"serviceType,omitempty"`
	Priority       string    `json:"priority,omitempty"`
	ScheduledDate  time.Time `json:"scheduledDate,omitempty"`
	TechnicianID   string    `json:"technicianId,omitempty"`
	EstimatedHours float64   `json:"estimatedHours,omitempty"`
}

// HVACSkillContext provides context for HVAC skill execution
type HVACSkillContext struct {
	CustomerData   map[string]interface{} `json:"customerData,omitempty"`
	EquipmentData  map[string]interface{} `json:"equipmentData,omitempty"`
	HistoricalData map[string]interface{} `json:"historicalData,omitempty"`
	WeatherData    map[string]interface{} `json:"weatherData,omitempty"`
	Preferences    map[string]interface{} `json:"preferences,omitempty"`
}
