package a2a

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

// 🌐 A2A Client - Google Agent-to-Agent Protocol Client
// Enables communication with other A2A-compliant agents

type A2AClient struct {
	logger     *log.Helper
	httpClient *http.Client
	userAgent  string
	timeout    time.Duration
}

// A2AClientConfig contains client configuration
type A2AClientConfig struct {
	UserAgent string
	Timeout   time.Duration
}

// AgentEndpoint represents a discovered A2A agent
type AgentEndpoint struct {
	URL         string     `json:"url"`
	AgentCard   *AgentCard `json:"agent_card"`
	LastChecked time.Time  `json:"last_checked"`
	Available   bool       `json:"available"`
}

// NewA2AClient creates a new A2A protocol client
func NewA2AClient(config *A2AClientConfig, logger log.Logger) *A2AClient {
	logHelper := log.NewHelper(logger)
	logHelper.Info("🌐 Initializing A2A Client")

	if config.Timeout == 0 {
		config.Timeout = 30 * time.Second
	}
	if config.UserAgent == "" {
		config.UserAgent = "HVAC-CRM-A2A-Client/1.0.0"
	}

	client := &A2AClient{
		logger:    logHelper,
		userAgent: config.UserAgent,
		timeout:   config.Timeout,
		httpClient: &http.Client{
			Timeout: config.Timeout,
		},
	}

	logHelper.Info("✅ A2A Client initialized successfully")
	return client
}

// DiscoverAgent discovers an A2A agent by URL
func (c *A2AClient) DiscoverAgent(ctx context.Context, baseURL string) (*AgentEndpoint, error) {
	c.logger.Infof("🔍 Discovering A2A agent at: %s", baseURL)

	// Try to fetch agent card
	agentCardURL := baseURL + "/.well-known/agent.json"
	agentCard, err := c.fetchAgentCard(ctx, agentCardURL)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch agent card: %w", err)
	}

	endpoint := &AgentEndpoint{
		URL:         baseURL,
		AgentCard:   agentCard,
		LastChecked: time.Now(),
		Available:   true,
	}

	c.logger.Infof("✅ Discovered agent: %s", agentCard.Name)
	return endpoint, nil
}

// SendMessage sends a message to an A2A agent
func (c *A2AClient) SendMessage(ctx context.Context, agentURL string, message *Message, config *MessageConfiguration) (*Task, error) {
	c.logger.Infof("📤 Sending message to agent: %s", agentURL)

	// Prepare JSON-RPC request
	request := JSONRPCRequest{
		JSONRPC: "2.0",
		ID:      fmt.Sprintf("client-%d", time.Now().UnixNano()),
		Method:  "message/send",
		Params: MessageSendParams{
			Message:       *message,
			Configuration: config,
		},
	}

	// Send request
	endpoint := agentURL + "/a2a/message/send"
	response, err := c.sendJSONRPCRequest(ctx, endpoint, &request)
	if err != nil {
		return nil, fmt.Errorf("failed to send message: %w", err)
	}

	// Parse response
	if response.Error != nil {
		return nil, fmt.Errorf("agent returned error: %s", response.Error.Message)
	}

	// Convert result to Task
	var task Task
	if err := c.parseResult(response.Result, &task); err != nil {
		return nil, fmt.Errorf("failed to parse task result: %w", err)
	}

	c.logger.Infof("✅ Message sent successfully, task ID: %s", task.ID)
	return &task, nil
}

// SendStreamingMessage sends a streaming message to an A2A agent
func (c *A2AClient) SendStreamingMessage(ctx context.Context, agentURL string, message *Message, config *MessageConfiguration, callback func(*SendStreamingMessageResponse)) error {
	c.logger.Infof("🌊 Sending streaming message to agent: %s", agentURL)

	// Prepare JSON-RPC request
	request := JSONRPCRequest{
		JSONRPC: "2.0",
		ID:      fmt.Sprintf("stream-%d", time.Now().UnixNano()),
		Method:  "message/stream",
		Params: MessageSendParams{
			Message:       *message,
			Configuration: config,
		},
	}

	// Send streaming request
	endpoint := agentURL + "/a2a/message/stream"
	return c.sendStreamingRequest(ctx, endpoint, &request, callback)
}

// GetTask retrieves a task from an A2A agent
func (c *A2AClient) GetTask(ctx context.Context, agentURL string, taskID string) (*Task, error) {
	c.logger.Infof("📋 Getting task %s from agent: %s", taskID, agentURL)

	// Prepare JSON-RPC request
	request := JSONRPCRequest{
		JSONRPC: "2.0",
		ID:      fmt.Sprintf("get-task-%d", time.Now().UnixNano()),
		Method:  "tasks/get",
		Params: TaskQueryParams{
			ID: taskID,
		},
	}

	// Send request
	endpoint := agentURL + "/a2a/tasks/get"
	response, err := c.sendJSONRPCRequest(ctx, endpoint, &request)
	if err != nil {
		return nil, fmt.Errorf("failed to get task: %w", err)
	}

	// Parse response
	if response.Error != nil {
		return nil, fmt.Errorf("agent returned error: %s", response.Error.Message)
	}

	// Convert result to Task
	var task Task
	if err := c.parseResult(response.Result, &task); err != nil {
		return nil, fmt.Errorf("failed to parse task result: %w", err)
	}

	return &task, nil
}

// CancelTask cancels a task on an A2A agent
func (c *A2AClient) CancelTask(ctx context.Context, agentURL string, taskID string) error {
	c.logger.Infof("❌ Canceling task %s on agent: %s", taskID, agentURL)

	// Prepare JSON-RPC request
	request := JSONRPCRequest{
		JSONRPC: "2.0",
		ID:      fmt.Sprintf("cancel-task-%d", time.Now().UnixNano()),
		Method:  "tasks/cancel",
		Params: TaskIdParams{
			ID: taskID,
		},
	}

	// Send request
	endpoint := agentURL + "/a2a/tasks/cancel"
	response, err := c.sendJSONRPCRequest(ctx, endpoint, &request)
	if err != nil {
		return fmt.Errorf("failed to cancel task: %w", err)
	}

	// Check for errors
	if response.Error != nil {
		return fmt.Errorf("agent returned error: %s", response.Error.Message)
	}

	c.logger.Infof("✅ Task %s canceled successfully", taskID)
	return nil
}

// fetchAgentCard fetches an agent card from a URL
func (c *A2AClient) fetchAgentCard(ctx context.Context, url string) (*AgentCard, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("User-Agent", c.userAgent)
	req.Header.Set("Accept", "application/json")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch agent card: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("agent card request failed with status: %d", resp.StatusCode)
	}

	var agentCard AgentCard
	if err := json.NewDecoder(resp.Body).Decode(&agentCard); err != nil {
		return nil, fmt.Errorf("failed to decode agent card: %w", err)
	}

	return &agentCard, nil
}

// sendJSONRPCRequest sends a JSON-RPC request and returns the response
func (c *A2AClient) sendJSONRPCRequest(ctx context.Context, url string, request *JSONRPCRequest) (*JSONRPCResponse, error) {
	// Marshal request
	requestBody, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create HTTP request
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", c.userAgent)

	// Send request
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// Read response
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// Parse JSON-RPC response
	var response JSONRPCResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	return &response, nil
}

// sendStreamingRequest sends a streaming request and processes SSE responses
func (c *A2AClient) sendStreamingRequest(ctx context.Context, url string, request *JSONRPCRequest, callback func(*SendStreamingMessageResponse)) error {
	// Marshal request
	requestBody, err := json.Marshal(request)
	if err != nil {
		return fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create HTTP request
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(requestBody))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", c.userAgent)
	req.Header.Set("Accept", "text/event-stream")

	// Send request
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// Process SSE stream
	return c.processSSEStream(resp.Body, callback)
}

// processSSEStream processes Server-Sent Events stream
func (c *A2AClient) processSSEStream(reader io.Reader, callback func(*SendStreamingMessageResponse)) error {
	decoder := json.NewDecoder(reader)
	
	for {
		var response JSONRPCResponse
		if err := decoder.Decode(&response); err != nil {
			if err == io.EOF {
				break
			}
			return fmt.Errorf("failed to decode SSE response: %w", err)
		}

		// Handle error responses
		if response.Error != nil {
			c.logger.Errorf("Received error in stream: %s", response.Error.Message)
			continue
		}

		// Parse streaming response
		var streamResponse SendStreamingMessageResponse
		if err := c.parseResult(response.Result, &streamResponse); err != nil {
			c.logger.Errorf("Failed to parse stream response: %v", err)
			continue
		}

		// Call callback
		if callback != nil {
			callback(&streamResponse)
		}

		// Check if this is the final response
		if streamResponse.Final {
			break
		}
	}

	return nil
}

// parseResult parses a JSON-RPC result into a target struct
func (c *A2AClient) parseResult(result interface{}, target interface{}) error {
	// Convert to JSON and back to parse into target struct
	jsonData, err := json.Marshal(result)
	if err != nil {
		return fmt.Errorf("failed to marshal result: %w", err)
	}

	if err := json.Unmarshal(jsonData, target); err != nil {
		return fmt.Errorf("failed to unmarshal result: %w", err)
	}

	return nil
}

// ValidateAgentCard validates an agent card structure
func (c *A2AClient) ValidateAgentCard(card *AgentCard) error {
	if card.Name == "" {
		return fmt.Errorf("agent name is required")
	}
	if card.URL == "" {
		return fmt.Errorf("agent URL is required")
	}
	if len(card.Skills) == 0 {
		return fmt.Errorf("at least one skill is required")
	}

	return nil
}

// GetSupportedSkills returns the skills supported by an agent
func (c *A2AClient) GetSupportedSkills(agentCard *AgentCard) []string {
	skills := make([]string, len(agentCard.Skills))
	for i, skill := range agentCard.Skills {
		skills[i] = skill.Name
	}
	return skills
}

// CheckAgentCompatibility checks if an agent supports required capabilities
func (c *A2AClient) CheckAgentCompatibility(agentCard *AgentCard, requiredCapabilities []string) bool {
	capabilities := agentCard.Capabilities

	for _, required := range requiredCapabilities {
		switch required {
		case "streaming":
			if !capabilities.Streaming {
				return false
			}
		case "pushNotifications":
			if !capabilities.PushNotifications {
				return false
			}
		case "multiTurn":
			if !capabilities.MultiTurn {
				return false
			}
		}
	}

	return true
}
