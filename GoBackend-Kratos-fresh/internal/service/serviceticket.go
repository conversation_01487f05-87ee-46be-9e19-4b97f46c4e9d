package service

import (
	"context"
	"encoding/json"

	"github.com/go-kratos/kratos/v2/log"

	pb "gobackend-hvac-kratos/api/serviceticket/v1"
	"gobackend-hvac-kratos/internal/biz"
	"gobackend-hvac-kratos/internal/entity"
)

// 🎫 Service Ticket Service - Comprehensive Ticket Management
// GoBackend-Kratos HVAC CRM System

// ServiceTicketService implements the service ticket service
type ServiceTicketService struct {
	pb.UnimplementedServiceTicketServiceServer

	serviceTicketUc *biz.ServiceTicketUsecase
	log             *log.Helper
}

// NewServiceTicketService creates a new service ticket service
func NewServiceTicketService(serviceTicketUc *biz.ServiceTicketUsecase, logger log.Logger) *ServiceTicketService {
	return &ServiceTicketService{
		serviceTicketUc: serviceTicketUc,
		log:             log.NewHelper(logger),
	}
}

// ============================================================================
// TICKET CRUD OPERATIONS
// ============================================================================

// CreateTicket creates a new service ticket
func (s *ServiceTicketService) CreateTicket(ctx context.Context, req *pb.CreateTicketRequest) (*pb.CreateTicketResponse, error) {
	s.log.WithContext(ctx).Infof("Creating service ticket: %s", req.Title)

	ticket := &entity.ServiceTicket{
		CustomerID:               req.CustomerId,
		Title:                    req.Title,
		Description:              req.Description,
		Type:                     convertProtoTicketTypeToEntity(req.Type),
		Priority:                 convertProtoTicketPriorityToEntity(req.Priority),
		Urgency:                  convertProtoTicketUrgencyToEntity(req.Urgency),
		EstimatedDurationMinutes: req.EstimatedDurationMinutes,
		ServiceAddress:           req.ServiceAddress,
		EstimatedCost:            req.EstimatedCost,
	}

	// Handle optional fields
	if req.EquipmentId > 0 {
		ticket.EquipmentID = &req.EquipmentId
	}
	if req.ScheduledDate != nil {
		scheduledDate := req.ScheduledDate.AsTime()
		ticket.ScheduledDate = &scheduledDate
	}
	if req.DueDate != nil {
		dueDate := req.DueDate.AsTime()
		ticket.DueDate = &dueDate
	}
	if req.Latitude != 0 {
		ticket.Latitude = &req.Latitude
	}
	if req.Longitude != 0 {
		ticket.Longitude = &req.Longitude
	}

	// Convert tags to JSON
	if len(req.Tags) > 0 {
		tagsJSON, _ := json.Marshal(req.Tags)
		ticket.Tags = tagsJSON
	}

	// Convert custom fields
	if req.CustomFields != nil {
		customFieldsJSON, _ := req.CustomFields.MarshalJSON()
		ticket.CustomFields = customFieldsJSON
	}

	result, err := s.serviceTicketUc.CreateTicket(ctx, ticket)
	if err != nil {
		return nil, err
	}

	return &pb.CreateTicketResponse{
		Ticket: convertServiceTicketToProto(result),
	}, nil
}

// GetTicket retrieves a service ticket by ID
func (s *ServiceTicketService) GetTicket(ctx context.Context, req *pb.GetTicketRequest) (*pb.GetTicketResponse, error) {
	s.log.WithContext(ctx).Infof("Getting service ticket: %d", req.Id)

	includeRelations := req.IncludeComments || req.IncludeAttachments || req.IncludeResources || req.IncludeHistory

	ticket, err := s.serviceTicketUc.GetTicket(ctx, req.Id, includeRelations)
	if err != nil {
		return nil, err
	}

	return &pb.GetTicketResponse{
		Ticket: convertServiceTicketToProto(ticket),
	}, nil
}

// ListTickets retrieves service tickets with filtering and pagination
func (s *ServiceTicketService) ListTickets(ctx context.Context, req *pb.ListTicketsRequest) (*pb.ListTicketsResponse, error) {
	s.log.WithContext(ctx).Infof("Listing service tickets: page=%d, size=%d", req.Page, req.PageSize)

	filters := biz.TicketFilters{
		Page:         req.Page,
		PageSize:     req.PageSize,
		CustomerID:   req.CustomerId,
		TechnicianID: req.TechnicianId,
		Status:       convertProtoTicketStatusToEntity(req.Status),
		Priority:     convertProtoTicketPriorityToEntity(req.Priority),
		Type:         convertProtoTicketTypeToEntity(req.Type),
		SearchQuery:  req.SearchQuery,
		Tags:         req.Tags,
		SortBy:       req.SortBy,
		SortOrder:    req.SortOrder,
	}

	if req.DateFrom != nil {
		dateFrom := req.DateFrom.AsTime()
		filters.DateFrom = &dateFrom
	}
	if req.DateTo != nil {
		dateTo := req.DateTo.AsTime()
		filters.DateTo = &dateTo
	}

	tickets, total, err := s.serviceTicketUc.ListTickets(ctx, filters)
	if err != nil {
		return nil, err
	}

	pbTickets := make([]*pb.ServiceTicket, len(tickets))
	for i, ticket := range tickets {
		pbTickets[i] = convertServiceTicketToProto(ticket)
	}

	return &pb.ListTicketsResponse{
		Tickets:    pbTickets,
		TotalCount: total,
		Page:       req.Page,
		PageSize:   req.PageSize,
	}, nil
}

// UpdateTicket updates an existing service ticket
func (s *ServiceTicketService) UpdateTicket(ctx context.Context, req *pb.UpdateTicketRequest) (*pb.UpdateTicketResponse, error) {
	s.log.WithContext(ctx).Infof("Updating service ticket: %d", req.Id)

	// Get existing ticket
	ticket, err := s.serviceTicketUc.GetTicket(ctx, req.Id, false)
	if err != nil {
		return nil, err
	}

	// Update fields
	if req.Title != "" {
		ticket.Title = req.Title
	}
	if req.Description != "" {
		ticket.Description = req.Description
	}
	if req.Priority != pb.TicketPriority_TICKET_PRIORITY_UNSPECIFIED {
		ticket.Priority = convertProtoTicketPriorityToEntity(req.Priority)
	}
	if req.Urgency != pb.TicketUrgency_TICKET_URGENCY_UNSPECIFIED {
		ticket.Urgency = convertProtoTicketUrgencyToEntity(req.Urgency)
	}
	if req.ScheduledDate != nil {
		scheduledDate := req.ScheduledDate.AsTime()
		ticket.ScheduledDate = &scheduledDate
	}
	if req.DueDate != nil {
		dueDate := req.DueDate.AsTime()
		ticket.DueDate = &dueDate
	}
	if req.EstimatedDurationMinutes > 0 {
		ticket.EstimatedDurationMinutes = req.EstimatedDurationMinutes
	}
	if req.ServiceAddress != "" {
		ticket.ServiceAddress = req.ServiceAddress
	}
	if req.Latitude != 0 {
		ticket.Latitude = &req.Latitude
	}
	if req.Longitude != 0 {
		ticket.Longitude = &req.Longitude
	}
	if req.EstimatedCost > 0 {
		ticket.EstimatedCost = req.EstimatedCost
	}

	// Update tags
	if len(req.Tags) > 0 {
		tagsJSON, _ := json.Marshal(req.Tags)
		ticket.Tags = tagsJSON
	}

	// Update custom fields
	if req.CustomFields != nil {
		customFieldsJSON, _ := req.CustomFields.MarshalJSON()
		ticket.CustomFields = customFieldsJSON
	}

	result, err := s.serviceTicketUc.UpdateTicket(ctx, ticket)
	if err != nil {
		return nil, err
	}

	return &pb.UpdateTicketResponse{
		Ticket: convertServiceTicketToProto(result),
	}, nil
}

// DeleteTicket deletes a service ticket by ID
func (s *ServiceTicketService) DeleteTicket(ctx context.Context, req *pb.DeleteTicketRequest) (*pb.DeleteTicketResponse, error) {
	s.log.WithContext(ctx).Infof("Deleting service ticket: %d", req.Id)

	err := s.serviceTicketUc.DeleteTicket(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	return &pb.DeleteTicketResponse{
		Success: true,
	}, nil
}

// ============================================================================
// STATUS MANAGEMENT
// ============================================================================

// UpdateTicketStatus updates the status of a service ticket
func (s *ServiceTicketService) UpdateTicketStatus(ctx context.Context, req *pb.UpdateTicketStatusRequest) (*pb.UpdateTicketStatusResponse, error) {
	s.log.WithContext(ctx).Infof("Updating ticket status: %d to %s", req.Id, req.NewStatus)

	newStatus := convertProtoTicketStatusToEntity(req.NewStatus)
	ticket, statusHistory, err := s.serviceTicketUc.UpdateTicketStatus(ctx, req.Id, newStatus, req.ChangedBy, req.Reason)
	if err != nil {
		return nil, err
	}

	return &pb.UpdateTicketStatusResponse{
		Ticket:       convertServiceTicketToProto(ticket),
		StatusChange: convertTicketStatusHistoryToProto(statusHistory),
	}, nil
}

// GetTicketHistory retrieves status change history for a ticket
func (s *ServiceTicketService) GetTicketHistory(ctx context.Context, req *pb.GetTicketHistoryRequest) (*pb.GetTicketHistoryResponse, error) {
	s.log.WithContext(ctx).Infof("Getting ticket history: %d", req.Id)

	history, total, err := s.serviceTicketUc.GetTicketHistory(ctx, req.Id, req.Page, req.PageSize)
	if err != nil {
		return nil, err
	}

	pbHistory := make([]*pb.TicketStatusHistory, len(history))
	for i, h := range history {
		pbHistory[i] = convertTicketStatusHistoryToProto(h)
	}

	return &pb.GetTicketHistoryResponse{
		History:    pbHistory,
		TotalCount: total,
	}, nil
}

// ============================================================================
// ASSIGNMENT MANAGEMENT
// ============================================================================

// AssignTicket assigns a ticket to a technician
func (s *ServiceTicketService) AssignTicket(ctx context.Context, req *pb.AssignTicketRequest) (*pb.AssignTicketResponse, error) {
	s.log.WithContext(ctx).Infof("Assigning ticket %d to technician %d", req.Id, req.TechnicianId)

	ticket, err := s.serviceTicketUc.AssignTicket(ctx, req.Id, req.TechnicianId, req.TeamMemberIds, req.AssignedBy)
	if err != nil {
		return nil, err
	}

	return &pb.AssignTicketResponse{
		Ticket: convertServiceTicketToProto(ticket),
	}, nil
}

// ReassignTicket reassigns a ticket to a different technician
func (s *ServiceTicketService) ReassignTicket(ctx context.Context, req *pb.ReassignTicketRequest) (*pb.ReassignTicketResponse, error) {
	s.log.WithContext(ctx).Infof("Reassigning ticket %d to technician %d", req.Id, req.NewTechnicianId)

	ticket, err := s.serviceTicketUc.AssignTicket(ctx, req.Id, req.NewTechnicianId, req.NewTeamMemberIds, req.ReassignedBy)
	if err != nil {
		return nil, err
	}

	return &pb.ReassignTicketResponse{
		Ticket: convertServiceTicketToProto(ticket),
	}, nil
}

// GetTechnicianTickets retrieves tickets assigned to a technician
func (s *ServiceTicketService) GetTechnicianTickets(ctx context.Context, req *pb.GetTechnicianTicketsRequest) (*pb.GetTechnicianTicketsResponse, error) {
	s.log.WithContext(ctx).Infof("Getting technician tickets: %d", req.TechnicianId)

	filters := biz.TechnicianTicketFilters{
		Page:     req.Page,
		PageSize: req.PageSize,
		Status:   convertProtoTicketStatusToEntity(req.Status),
	}

	if req.DateFrom != nil {
		dateFrom := req.DateFrom.AsTime()
		filters.DateFrom = &dateFrom
	}
	if req.DateTo != nil {
		dateTo := req.DateTo.AsTime()
		filters.DateTo = &dateTo
	}

	tickets, total, err := s.serviceTicketUc.GetTechnicianTickets(ctx, req.TechnicianId, filters)
	if err != nil {
		return nil, err
	}

	pbTickets := make([]*pb.ServiceTicket, len(tickets))
	for i, ticket := range tickets {
		pbTickets[i] = convertServiceTicketToProto(ticket)
	}

	// Create workload summary
	workload := &pb.TechnicianWorkload{
		TechnicianId:          req.TechnicianId,
		ActiveTickets:         int32(len(tickets)), // Simplified
		OverdueTickets:        0,                   // Would be calculated
		ScheduledToday:        0,                   // Would be calculated
		EstimatedHoursToday:   0,                   // Would be calculated
		UtilizationPercentage: 75.0,                // Would be calculated
	}

	return &pb.GetTechnicianTicketsResponse{
		Tickets:    pbTickets,
		TotalCount: total,
		Workload:   workload,
	}, nil
}

// ============================================================================
// COMMUNICATION & COMMENTS
// ============================================================================

// AddComment adds a comment to a ticket
func (s *ServiceTicketService) AddComment(ctx context.Context, req *pb.AddCommentRequest) (*pb.AddCommentResponse, error) {
	s.log.WithContext(ctx).Infof("Adding comment to ticket: %d", req.Id)

	comment := &entity.TicketComment{
		TicketID:   req.Id,
		AuthorID:   req.AuthorId,
		Content:    req.Content,
		IsInternal: req.IsInternal,
	}

	result, err := s.serviceTicketUc.AddComment(ctx, comment)
	if err != nil {
		return nil, err
	}

	return &pb.AddCommentResponse{
		Comment: convertTicketCommentToProto(result),
	}, nil
}

// GetComments retrieves comments for a ticket
func (s *ServiceTicketService) GetComments(ctx context.Context, req *pb.GetCommentsRequest) (*pb.GetCommentsResponse, error) {
	s.log.WithContext(ctx).Infof("Getting comments for ticket: %d", req.Id)

	comments, total, err := s.serviceTicketUc.GetComments(ctx, req.Id, req.Page, req.PageSize, req.IncludeInternal)
	if err != nil {
		return nil, err
	}

	pbComments := make([]*pb.TicketComment, len(comments))
	for i, comment := range comments {
		pbComments[i] = convertTicketCommentToProto(comment)
	}

	return &pb.GetCommentsResponse{
		Comments:   pbComments,
		TotalCount: total,
	}, nil
}

// ============================================================================
// ATTACHMENTS MANAGEMENT
// ============================================================================

// AddAttachment adds an attachment to a ticket
func (s *ServiceTicketService) AddAttachment(ctx context.Context, req *pb.AddAttachmentRequest) (*pb.AddAttachmentResponse, error) {
	s.log.WithContext(ctx).Infof("Adding attachment to ticket: %d", req.Id)

	attachment := &entity.TicketAttachment{
		TicketID:    req.Id,
		Filename:    req.Filename,
		FileType:    req.FileType,
		FileSize:    req.FileSize,
		FileURL:     req.FileUrl,
		Description: req.Description,
		UploadedBy:  "User", // Would be populated from context
	}

	result, err := s.serviceTicketUc.AddAttachment(ctx, attachment)
	if err != nil {
		return nil, err
	}

	return &pb.AddAttachmentResponse{
		Attachment: convertTicketAttachmentToProto(result),
	}, nil
}

// GetAttachments retrieves attachments for a ticket
func (s *ServiceTicketService) GetAttachments(ctx context.Context, req *pb.GetAttachmentsRequest) (*pb.GetAttachmentsResponse, error) {
	s.log.WithContext(ctx).Infof("Getting attachments for ticket: %d", req.Id)

	attachments, err := s.serviceTicketUc.GetAttachments(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	pbAttachments := make([]*pb.TicketAttachment, len(attachments))
	for i, attachment := range attachments {
		pbAttachments[i] = convertTicketAttachmentToProto(attachment)
	}

	return &pb.GetAttachmentsResponse{
		Attachments: pbAttachments,
	}, nil
}

// ============================================================================
// RESOURCE MANAGEMENT
// ============================================================================

// AddResource adds a resource to a ticket
func (s *ServiceTicketService) AddResource(ctx context.Context, req *pb.AddResourceRequest) (*pb.AddResourceResponse, error) {
	s.log.WithContext(ctx).Infof("Adding resource to ticket: %d", req.Id)

	resource := &entity.TicketResource{
		TicketID:        req.Id,
		ResourceType:    req.ResourceType,
		ResourceName:    req.ResourceName,
		PartNumber:      req.PartNumber,
		QuantityPlanned: req.QuantityPlanned,
		UnitCost:        req.UnitCost,
		Supplier:        req.Supplier,
		Notes:           req.Notes,
	}

	result, err := s.serviceTicketUc.AddResource(ctx, resource)
	if err != nil {
		return nil, err
	}

	return &pb.AddResourceResponse{
		Resource: convertTicketResourceToProto(result),
	}, nil
}

// GetResources retrieves resources for a ticket
func (s *ServiceTicketService) GetResources(ctx context.Context, req *pb.GetResourcesRequest) (*pb.GetResourcesResponse, error) {
	s.log.WithContext(ctx).Infof("Getting resources for ticket: %d", req.Id)

	resources, err := s.serviceTicketUc.GetResources(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	pbResources := make([]*pb.TicketResource, len(resources))
	var totalEstimated, totalActual float64

	for i, resource := range resources {
		pbResources[i] = convertTicketResourceToProto(resource)
		totalEstimated += float64(resource.QuantityPlanned) * resource.UnitCost
		totalActual += resource.TotalCost
	}

	return &pb.GetResourcesResponse{
		Resources:          pbResources,
		TotalEstimatedCost: totalEstimated,
		TotalActualCost:    totalActual,
	}, nil
}

// UpdateResourceUsage updates resource usage information
func (s *ServiceTicketService) UpdateResourceUsage(ctx context.Context, req *pb.UpdateResourceUsageRequest) (*pb.UpdateResourceUsageResponse, error) {
	s.log.WithContext(ctx).Infof("Updating resource usage: ticket=%d, resource=%d", req.Id, req.ResourceId)

	resource, err := s.serviceTicketUc.UpdateResourceUsage(ctx, req.Id, req.ResourceId, req.QuantityUsed, req.ActualUnitCost, req.UsageNotes)
	if err != nil {
		return nil, err
	}

	return &pb.UpdateResourceUsageResponse{
		Resource: convertTicketResourceToProto(resource),
	}, nil
}

// ============================================================================
// ANALYTICS & REPORTING
// ============================================================================

// GetTicketAnalytics retrieves comprehensive ticket analytics
func (s *ServiceTicketService) GetTicketAnalytics(ctx context.Context, req *pb.GetTicketAnalyticsRequest) (*pb.GetTicketAnalyticsResponse, error) {
	s.log.WithContext(ctx).Info("Getting ticket analytics")

	filters := biz.AnalyticsFilters{
		DateFrom: req.DateFrom.AsTime(),
		DateTo:   req.DateTo.AsTime(),
	}

	if req.TechnicianId > 0 {
		filters.TechnicianID = &req.TechnicianId
	}
	if req.CustomerId > 0 {
		filters.CustomerID = &req.CustomerId
	}
	if req.Type != pb.TicketType_TICKET_TYPE_UNSPECIFIED {
		ticketType := convertProtoTicketTypeToEntity(req.Type)
		filters.Type = &ticketType
	}

	analytics, err := s.serviceTicketUc.GetTicketAnalytics(ctx, filters)
	if err != nil {
		return nil, err
	}

	return &pb.GetTicketAnalyticsResponse{
		Analytics: convertTicketAnalyticsToProto(analytics),
	}, nil
}

// GetTechnicianPerformance retrieves technician performance metrics
func (s *ServiceTicketService) GetTechnicianPerformance(ctx context.Context, req *pb.GetTechnicianPerformanceRequest) (*pb.GetTechnicianPerformanceResponse, error) {
	s.log.WithContext(ctx).Infof("Getting technician performance: %d", req.TechnicianId)

	dateFrom := req.DateFrom.AsTime()
	dateTo := req.DateTo.AsTime()

	performance, err := s.serviceTicketUc.GetTechnicianPerformance(ctx, req.TechnicianId, dateFrom, dateTo)
	if err != nil {
		return nil, err
	}

	return &pb.GetTechnicianPerformanceResponse{
		Performance: convertTechnicianPerformanceToProto(performance),
	}, nil
}

// GetSLAReport retrieves SLA compliance report
func (s *ServiceTicketService) GetSLAReport(ctx context.Context, req *pb.GetSLAReportRequest) (*pb.GetSLAReportResponse, error) {
	s.log.WithContext(ctx).Info("Getting SLA report")

	dateFrom := req.DateFrom.AsTime()
	dateTo := req.DateTo.AsTime()

	var priority *entity.TicketPriority
	if req.Priority != pb.TicketPriority_TICKET_PRIORITY_UNSPECIFIED {
		p := convertProtoTicketPriorityToEntity(req.Priority)
		priority = &p
	}

	report, err := s.serviceTicketUc.GetSLAReport(ctx, dateFrom, dateTo, priority)
	if err != nil {
		return nil, err
	}

	return &pb.GetSLAReportResponse{
		Report: convertSLAReportToProto(report),
	}, nil
}

// ============================================================================
// CUSTOMER FEATURES
// ============================================================================

// RateService allows customers to rate service quality
func (s *ServiceTicketService) RateService(ctx context.Context, req *pb.RateServiceRequest) (*pb.RateServiceResponse, error) {
	s.log.WithContext(ctx).Infof("Rating service for ticket: %d", req.Id)

	err := s.serviceTicketUc.RateService(ctx, req.Id, req.Rating, req.Feedback, req.CustomerId)
	if err != nil {
		return nil, err
	}

	return &pb.RateServiceResponse{
		Success:          true,
		NewAverageRating: float64(req.Rating), // Simplified - would calculate actual average
	}, nil
}

// GetCustomerTickets retrieves tickets for a customer
func (s *ServiceTicketService) GetCustomerTickets(ctx context.Context, req *pb.GetCustomerTicketsRequest) (*pb.GetCustomerTicketsResponse, error) {
	s.log.WithContext(ctx).Infof("Getting customer tickets: %d", req.CustomerId)

	filters := biz.CustomerTicketFilters{
		Page:     req.Page,
		PageSize: req.PageSize,
		Status:   convertProtoTicketStatusToEntity(req.Status),
	}

	if req.DateFrom != nil {
		dateFrom := req.DateFrom.AsTime()
		filters.DateFrom = &dateFrom
	}
	if req.DateTo != nil {
		dateTo := req.DateTo.AsTime()
		filters.DateTo = &dateTo
	}

	tickets, total, summary, err := s.serviceTicketUc.GetCustomerTickets(ctx, req.CustomerId, filters)
	if err != nil {
		return nil, err
	}

	pbTickets := make([]*pb.ServiceTicket, len(tickets))
	for i, ticket := range tickets {
		pbTickets[i] = convertServiceTicketToProto(ticket)
	}

	return &pb.GetCustomerTicketsResponse{
		Tickets:    pbTickets,
		TotalCount: total,
		Summary:    convertCustomerTicketSummaryToProto(summary),
	}, nil
}
