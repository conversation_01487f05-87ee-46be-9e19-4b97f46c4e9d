package service

import (
	"encoding/json"

	"google.golang.org/protobuf/types/known/timestamppb"

	pb "gobackend-hvac-kratos/api/serviceticket/v1"
	"gobackend-hvac-kratos/internal/biz"
	"gobackend-hvac-kratos/internal/entity"
)

// 🔧 Service Ticket Helper Functions
// GoBackend-Kratos HVAC CRM System

// ============================================================================
// ENUM CONVERSION HELPERS
// ============================================================================

// convertProtoTicketTypeToEntity converts protobuf ticket type to entity
func convertProtoTicketTypeToEntity(pbType pb.TicketType) entity.TicketType {
	switch pbType {
	case pb.TicketType_TICKET_TYPE_INSTALLATION:
		return entity.TicketTypeInstallation
	case pb.TicketType_TICKET_TYPE_MAINTENANCE:
		return entity.TicketTypeMaintenance
	case pb.TicketType_TICKET_TYPE_REPAIR:
		return entity.TicketTypeRepair
	case pb.TicketType_TICKET_TYPE_INSPECTION:
		return entity.TicketTypeInspection
	case pb.TicketType_TICKET_TYPE_EMERGENCY:
		return entity.TicketTypeEmergency
	case pb.TicketType_TICKET_TYPE_CONSULTATION:
		return entity.TicketTypeConsultation
	case pb.TicketType_TICKET_TYPE_UPGRADE:
		return entity.TicketTypeUpgrade
	case pb.TicketType_TICKET_TYPE_REMOVAL:
		return entity.TicketTypeRemoval
	default:
		return entity.TicketTypeMaintenance
	}
}

// convertEntityTicketTypeToProto converts entity ticket type to protobuf
func convertEntityTicketTypeToProto(entityType entity.TicketType) pb.TicketType {
	switch entityType {
	case entity.TicketTypeInstallation:
		return pb.TicketType_TICKET_TYPE_INSTALLATION
	case entity.TicketTypeMaintenance:
		return pb.TicketType_TICKET_TYPE_MAINTENANCE
	case entity.TicketTypeRepair:
		return pb.TicketType_TICKET_TYPE_REPAIR
	case entity.TicketTypeInspection:
		return pb.TicketType_TICKET_TYPE_INSPECTION
	case entity.TicketTypeEmergency:
		return pb.TicketType_TICKET_TYPE_EMERGENCY
	case entity.TicketTypeConsultation:
		return pb.TicketType_TICKET_TYPE_CONSULTATION
	case entity.TicketTypeUpgrade:
		return pb.TicketType_TICKET_TYPE_UPGRADE
	case entity.TicketTypeRemoval:
		return pb.TicketType_TICKET_TYPE_REMOVAL
	default:
		return pb.TicketType_TICKET_TYPE_UNSPECIFIED
	}
}

// convertProtoTicketStatusToEntity converts protobuf ticket status to entity
func convertProtoTicketStatusToEntity(pbStatus pb.TicketStatus) entity.TicketStatus {
	switch pbStatus {
	case pb.TicketStatus_TICKET_STATUS_NEW:
		return entity.TicketStatusNew
	case pb.TicketStatus_TICKET_STATUS_ASSIGNED:
		return entity.TicketStatusAssigned
	case pb.TicketStatus_TICKET_STATUS_IN_PROGRESS:
		return entity.TicketStatusInProgress
	case pb.TicketStatus_TICKET_STATUS_ON_HOLD:
		return entity.TicketStatusOnHold
	case pb.TicketStatus_TICKET_STATUS_WAITING_PARTS:
		return entity.TicketStatusWaitingParts
	case pb.TicketStatus_TICKET_STATUS_WAITING_CUSTOMER:
		return entity.TicketStatusWaitingCustomer
	case pb.TicketStatus_TICKET_STATUS_COMPLETED:
		return entity.TicketStatusCompleted
	case pb.TicketStatus_TICKET_STATUS_CLOSED:
		return entity.TicketStatusClosed
	case pb.TicketStatus_TICKET_STATUS_CANCELLED:
		return entity.TicketStatusCancelled
	default:
		return entity.TicketStatusNew
	}
}

// convertEntityTicketStatusToProto converts entity ticket status to protobuf
func convertEntityTicketStatusToProto(entityStatus entity.TicketStatus) pb.TicketStatus {
	switch entityStatus {
	case entity.TicketStatusNew:
		return pb.TicketStatus_TICKET_STATUS_NEW
	case entity.TicketStatusAssigned:
		return pb.TicketStatus_TICKET_STATUS_ASSIGNED
	case entity.TicketStatusInProgress:
		return pb.TicketStatus_TICKET_STATUS_IN_PROGRESS
	case entity.TicketStatusOnHold:
		return pb.TicketStatus_TICKET_STATUS_ON_HOLD
	case entity.TicketStatusWaitingParts:
		return pb.TicketStatus_TICKET_STATUS_WAITING_PARTS
	case entity.TicketStatusWaitingCustomer:
		return pb.TicketStatus_TICKET_STATUS_WAITING_CUSTOMER
	case entity.TicketStatusCompleted:
		return pb.TicketStatus_TICKET_STATUS_COMPLETED
	case entity.TicketStatusClosed:
		return pb.TicketStatus_TICKET_STATUS_CLOSED
	case entity.TicketStatusCancelled:
		return pb.TicketStatus_TICKET_STATUS_CANCELLED
	default:
		return pb.TicketStatus_TICKET_STATUS_UNSPECIFIED
	}
}

// convertProtoTicketPriorityToEntity converts protobuf ticket priority to entity
func convertProtoTicketPriorityToEntity(pbPriority pb.TicketPriority) entity.TicketPriority {
	switch pbPriority {
	case pb.TicketPriority_TICKET_PRIORITY_LOW:
		return entity.TicketPriorityLow
	case pb.TicketPriority_TICKET_PRIORITY_NORMAL:
		return entity.TicketPriorityNormal
	case pb.TicketPriority_TICKET_PRIORITY_HIGH:
		return entity.TicketPriorityHigh
	case pb.TicketPriority_TICKET_PRIORITY_CRITICAL:
		return entity.TicketPriorityCritical
	default:
		return entity.TicketPriorityNormal
	}
}

// convertEntityTicketPriorityToProto converts entity ticket priority to protobuf
func convertEntityTicketPriorityToProto(entityPriority entity.TicketPriority) pb.TicketPriority {
	switch entityPriority {
	case entity.TicketPriorityLow:
		return pb.TicketPriority_TICKET_PRIORITY_LOW
	case entity.TicketPriorityNormal:
		return pb.TicketPriority_TICKET_PRIORITY_NORMAL
	case entity.TicketPriorityHigh:
		return pb.TicketPriority_TICKET_PRIORITY_HIGH
	case entity.TicketPriorityCritical:
		return pb.TicketPriority_TICKET_PRIORITY_CRITICAL
	default:
		return pb.TicketPriority_TICKET_PRIORITY_UNSPECIFIED
	}
}

// convertProtoTicketUrgencyToEntity converts protobuf ticket urgency to entity
func convertProtoTicketUrgencyToEntity(pbUrgency pb.TicketUrgency) entity.TicketUrgency {
	switch pbUrgency {
	case pb.TicketUrgency_TICKET_URGENCY_LOW:
		return entity.TicketUrgencyLow
	case pb.TicketUrgency_TICKET_URGENCY_MEDIUM:
		return entity.TicketUrgencyMedium
	case pb.TicketUrgency_TICKET_URGENCY_HIGH:
		return entity.TicketUrgencyHigh
	case pb.TicketUrgency_TICKET_URGENCY_EMERGENCY:
		return entity.TicketUrgencyEmergency
	default:
		return entity.TicketUrgencyMedium
	}
}

// convertEntityTicketUrgencyToProto converts entity ticket urgency to protobuf
func convertEntityTicketUrgencyToProto(entityUrgency entity.TicketUrgency) pb.TicketUrgency {
	switch entityUrgency {
	case entity.TicketUrgencyLow:
		return pb.TicketUrgency_TICKET_URGENCY_LOW
	case entity.TicketUrgencyMedium:
		return pb.TicketUrgency_TICKET_URGENCY_MEDIUM
	case entity.TicketUrgencyHigh:
		return pb.TicketUrgency_TICKET_URGENCY_HIGH
	case entity.TicketUrgencyEmergency:
		return pb.TicketUrgency_TICKET_URGENCY_EMERGENCY
	default:
		return pb.TicketUrgency_TICKET_URGENCY_UNSPECIFIED
	}
}

// ============================================================================
// ENTITY CONVERSION HELPERS
// ============================================================================

// convertServiceTicketToProto converts entity service ticket to protobuf
func convertServiceTicketToProto(ticket *entity.ServiceTicket) *pb.ServiceTicket {
	pbTicket := &pb.ServiceTicket{
		Id:                       ticket.ID,
		TicketNumber:             ticket.TicketNumber,
		CustomerId:               ticket.CustomerID,
		Title:                    ticket.Title,
		Description:              ticket.Description,
		Type:                     convertEntityTicketTypeToProto(ticket.Type),
		Status:                   convertEntityTicketStatusToProto(ticket.Status),
		Priority:                 convertEntityTicketPriorityToProto(ticket.Priority),
		Urgency:                  convertEntityTicketUrgencyToProto(ticket.Urgency),
		EstimatedDurationMinutes: ticket.EstimatedDurationMinutes,
		ServiceAddress:           ticket.ServiceAddress,
		EstimatedCost:            ticket.EstimatedCost,
		ActualCost:               ticket.ActualCost,
		LaborCost:                ticket.LaborCost,
		PartsCost:                ticket.PartsCost,
		SlaBreached:              ticket.SLABreached,
		CreatedAt:                timestamppb.New(ticket.CreatedAt),
		UpdatedAt:                timestamppb.New(ticket.UpdatedAt),
	}

	// Handle optional fields
	if ticket.EquipmentID != nil {
		pbTicket.EquipmentId = *ticket.EquipmentID
	}
	if ticket.ScheduledDate != nil {
		pbTicket.ScheduledDate = timestamppb.New(*ticket.ScheduledDate)
	}
	if ticket.DueDate != nil {
		pbTicket.DueDate = timestamppb.New(*ticket.DueDate)
	}
	if ticket.ActualDurationMinutes != nil {
		pbTicket.ActualDurationMinutes = *ticket.ActualDurationMinutes
	}
	if ticket.AssignedTechnicianID != nil {
		pbTicket.AssignedTechnicianId = *ticket.AssignedTechnicianID
	}
	if ticket.Latitude != nil {
		pbTicket.Latitude = *ticket.Latitude
	}
	if ticket.Longitude != nil {
		pbTicket.Longitude = *ticket.Longitude
	}
	if ticket.SLADeadline != nil {
		pbTicket.SlaDeadline = timestamppb.New(*ticket.SLADeadline)
	}
	if ticket.ResponseTimeMinutes != nil {
		pbTicket.ResponseTimeMinutes = *ticket.ResponseTimeMinutes
	}
	if ticket.ResolutionTimeMinutes != nil {
		pbTicket.ResolutionTimeMinutes = *ticket.ResolutionTimeMinutes
	}
	if ticket.CustomerRating != nil {
		pbTicket.CustomerRating = *ticket.CustomerRating
	}
	if ticket.CompletedAt != nil {
		pbTicket.CompletedAt = timestamppb.New(*ticket.CompletedAt)
	}
	if ticket.ClosedAt != nil {
		pbTicket.ClosedAt = timestamppb.New(*ticket.ClosedAt)
	}

	// Convert JSON fields
	if ticket.TeamMemberIDs != nil {
		var teamMemberIDs []int64
		if err := json.Unmarshal(ticket.TeamMemberIDs, &teamMemberIDs); err == nil {
			pbTicket.TeamMemberIds = teamMemberIDs
		}
	}
	if ticket.Tags != nil {
		var tags []string
		if err := json.Unmarshal(ticket.Tags, &tags); err == nil {
			pbTicket.Tags = tags
		}
	}

	pbTicket.CustomerFeedback = ticket.CustomerFeedback

	// Convert related entities
	if len(ticket.Comments) > 0 {
		pbTicket.Comments = make([]*pb.TicketComment, len(ticket.Comments))
		for i, comment := range ticket.Comments {
			pbTicket.Comments[i] = convertTicketCommentToProto(&comment)
		}
	}
	if len(ticket.Attachments) > 0 {
		pbTicket.Attachments = make([]*pb.TicketAttachment, len(ticket.Attachments))
		for i, attachment := range ticket.Attachments {
			pbTicket.Attachments[i] = convertTicketAttachmentToProto(&attachment)
		}
	}
	if len(ticket.Resources) > 0 {
		pbTicket.Resources = make([]*pb.TicketResource, len(ticket.Resources))
		for i, resource := range ticket.Resources {
			pbTicket.Resources[i] = convertTicketResourceToProto(&resource)
		}
	}
	if len(ticket.StatusHistory) > 0 {
		pbTicket.StatusHistory = make([]*pb.TicketStatusHistory, len(ticket.StatusHistory))
		for i, history := range ticket.StatusHistory {
			pbTicket.StatusHistory[i] = convertTicketStatusHistoryToProto(&history)
		}
	}

	return pbTicket
}

// convertTicketCommentToProto converts entity ticket comment to protobuf
func convertTicketCommentToProto(comment *entity.TicketComment) *pb.TicketComment {
	return &pb.TicketComment{
		Id:                comment.ID,
		TicketId:          comment.TicketID,
		AuthorId:          comment.AuthorID,
		AuthorName:        comment.AuthorName,
		AuthorType:        comment.AuthorType,
		Content:           comment.Content,
		IsInternal:        comment.IsInternal,
		IsSystemGenerated: comment.IsSystemGenerated,
		CreatedAt:         timestamppb.New(comment.CreatedAt),
	}
}

// convertTicketAttachmentToProto converts entity ticket attachment to protobuf
func convertTicketAttachmentToProto(attachment *entity.TicketAttachment) *pb.TicketAttachment {
	return &pb.TicketAttachment{
		Id:          attachment.ID,
		TicketId:    attachment.TicketID,
		Filename:    attachment.Filename,
		FileType:    attachment.FileType,
		FileSize:    attachment.FileSize,
		FileUrl:     attachment.FileURL,
		Description: attachment.Description,
		UploadedBy:  attachment.UploadedBy,
		UploadedAt:  timestamppb.New(attachment.UploadedAt),
	}
}

// convertTicketResourceToProto converts entity ticket resource to protobuf
func convertTicketResourceToProto(resource *entity.TicketResource) *pb.TicketResource {
	return &pb.TicketResource{
		Id:              resource.ID,
		TicketId:        resource.TicketID,
		ResourceType:    resource.ResourceType,
		ResourceName:    resource.ResourceName,
		PartNumber:      resource.PartNumber,
		QuantityPlanned: resource.QuantityPlanned,
		QuantityUsed:    resource.QuantityUsed,
		UnitCost:        resource.UnitCost,
		TotalCost:       resource.TotalCost,
		Supplier:        resource.Supplier,
		Notes:           resource.Notes,
		AddedAt:         timestamppb.New(resource.AddedAt),
	}
}

// convertTicketStatusHistoryToProto converts entity ticket status history to protobuf
func convertTicketStatusHistoryToProto(history *entity.TicketStatusHistory) *pb.TicketStatusHistory {
	return &pb.TicketStatusHistory{
		Id:           history.ID,
		TicketId:     history.TicketID,
		FromStatus:   convertEntityTicketStatusToProto(history.FromStatus),
		ToStatus:     convertEntityTicketStatusToProto(history.ToStatus),
		ChangedBy:    history.ChangedBy,
		ChangeReason: history.ChangeReason,
		ChangedAt:    timestamppb.New(history.ChangedAt),
	}
}

// convertTicketAnalyticsToProto converts biz ticket analytics to protobuf
func convertTicketAnalyticsToProto(analytics *biz.TicketAnalytics) *pb.TicketAnalytics {
	pbTypeStats := make([]*pb.TicketTypeStats, len(analytics.TypeStats))
	for i, stat := range analytics.TypeStats {
		pbTypeStats[i] = &pb.TicketTypeStats{
			Type:                 convertEntityTicketTypeToProto(stat.Type),
			Count:                stat.Count,
			AverageDurationHours: stat.AverageDurationHours,
			AverageCost:          stat.AverageCost,
		}
	}

	pbTechStats := make([]*pb.TechnicianStats, len(analytics.TechnicianStats))
	for i, stat := range analytics.TechnicianStats {
		pbTechStats[i] = &pb.TechnicianStats{
			TechnicianId:               stat.TechnicianID,
			TechnicianName:             stat.TechnicianName,
			TicketsCompleted:           stat.TicketsCompleted,
			AverageResolutionTimeHours: stat.AverageResolutionTimeHours,
			CustomerRatingAverage:      stat.CustomerRatingAverage,
			UtilizationRate:            stat.UtilizationRate,
		}
	}

	return &pb.TicketAnalytics{
		TotalTickets:               analytics.TotalTickets,
		CompletedTickets:           analytics.CompletedTickets,
		OverdueTickets:             analytics.OverdueTickets,
		CompletionRate:             analytics.CompletionRate,
		AverageResolutionTimeHours: analytics.AverageResolutionTimeHours,
		AverageResponseTimeHours:   analytics.AverageResponseTimeHours,
		SlaComplianceRate:          analytics.SLAComplianceRate,
		TotalRevenue:               analytics.TotalRevenue,
		AverageTicketValue:         analytics.AverageTicketValue,
		TypeStats:                  pbTypeStats,
		TechnicianStats:            pbTechStats,
		SatisfactionStats:          []*pb.CustomerSatisfactionStats{convertCustomerSatisfactionStatsToProto(&analytics.SatisfactionStats)},
	}
}

// convertTechnicianPerformanceToProto converts biz technician performance to protobuf
func convertTechnicianPerformanceToProto(performance *biz.TechnicianPerformance) *pb.TechnicianPerformance {
	return &pb.TechnicianPerformance{
		TechnicianId:               performance.TechnicianID,
		TechnicianName:             performance.TechnicianName,
		TicketsAssigned:            performance.TicketsAssigned,
		TicketsCompleted:           performance.TicketsCompleted,
		TicketsOverdue:             performance.TicketsOverdue,
		CompletionRate:             performance.CompletionRate,
		AverageResolutionTimeHours: performance.AverageResolutionTimeHours,
		CustomerRatingAverage:      performance.CustomerRatingAverage,
		RevenueGenerated:           performance.RevenueGenerated,
		HoursWorked:                performance.HoursWorked,
		EfficiencyScore:            performance.EfficiencyScore,
		Strengths:                  performance.Strengths,
		ImprovementAreas:           performance.ImprovementAreas,
	}
}

// convertSLAReportToProto converts biz SLA report to protobuf
func convertSLAReportToProto(report *biz.SLAReport) *pb.SLAReport {
	pbBreaches := make([]*pb.SLABreachDetail, len(report.Breaches))
	for i, breach := range report.Breaches {
		pbBreaches[i] = &pb.SLABreachDetail{
			TicketId:            breach.TicketID,
			TicketNumber:        breach.TicketNumber,
			Priority:            convertEntityTicketPriorityToProto(breach.Priority),
			SlaDeadline:         timestamppb.New(breach.SLADeadline),
			ActualCompletion:    timestamppb.New(breach.ActualCompletion),
			BreachDurationHours: breach.BreachDurationHours,
			BreachReason:        breach.BreachReason,
		}
	}

	pbPriorityStats := make([]*pb.PrioritySLAStats, len(report.PriorityStats))
	for i, stat := range report.PriorityStats {
		pbPriorityStats[i] = &pb.PrioritySLAStats{
			Priority:                 convertEntityTicketPriorityToProto(stat.Priority),
			TotalTickets:             stat.TotalTickets,
			SlaMet:                   stat.SLAMet,
			ComplianceRate:           stat.ComplianceRate,
			TargetResponseTimeHours:  stat.TargetResponseTimeHours,
			AverageResponseTimeHours: stat.AverageResponseTimeHours,
		}
	}

	return &pb.SLAReport{
		TotalTickets:      report.TotalTickets,
		SlaMet:            report.SLAMet,
		SlaBreached:       report.SLABreached,
		SlaComplianceRate: report.SLAComplianceRate,
		Breaches:          pbBreaches,
		PriorityStats:     pbPriorityStats,
	}
}

// convertCustomerTicketSummaryToProto converts biz customer ticket summary to protobuf
func convertCustomerTicketSummaryToProto(summary *biz.CustomerTicketSummary) *pb.CustomerTicketSummary {
	pbSummary := &pb.CustomerTicketSummary{
		CustomerId:       summary.CustomerID,
		TotalTickets:     summary.TotalTickets,
		OpenTickets:      summary.OpenTickets,
		CompletedTickets: summary.CompletedTickets,
		AverageRating:    summary.AverageRating,
		TotalSpent:       summary.TotalSpent,
	}

	if summary.LastServiceDate != nil {
		pbSummary.LastServiceDate = timestamppb.New(*summary.LastServiceDate)
	}
	if summary.NextScheduledService != nil {
		pbSummary.NextScheduledService = timestamppb.New(*summary.NextScheduledService)
	}

	return pbSummary
}

// convertCustomerSatisfactionStatsToProto converts biz customer satisfaction stats to protobuf
func convertCustomerSatisfactionStatsToProto(stats *biz.CustomerSatisfactionStats) *pb.CustomerSatisfactionStats {
	return &pb.CustomerSatisfactionStats{
		AverageRating:  stats.AverageRating,
		TotalRatings:   stats.TotalRatings,
		FiveStarCount:  stats.FiveStarCount,
		FourStarCount:  stats.FourStarCount,
		ThreeStarCount: stats.ThreeStarCount,
		TwoStarCount:   stats.TwoStarCount,
		OneStarCount:   stats.OneStarCount,
	}
}
