package entity

import (
	"time"
	"encoding/json"
	"fmt"
)

// 🎫 Service Ticket Entity - Comprehensive Ticket Management
// GoBackend-Kratos HVAC CRM System

// ServiceTicket represents a comprehensive service ticket
type ServiceTicket struct {
	ID                      int64                  `json:"id" gorm:"primaryKey;autoIncrement"`
	TicketNumber            string                 `json:"ticket_number" gorm:"uniqueIndex;size:50"`
	CustomerID              int64                  `json:"customer_id" gorm:"not null;index"`
	EquipmentID             *int64                 `json:"equipment_id" gorm:"index"`
	Title                   string                 `json:"title" gorm:"not null;size:255"`
	Description             string                 `json:"description" gorm:"type:text"`
	Type                    TicketType             `json:"type" gorm:"not null"`
	Status                  TicketStatus           `json:"status" gorm:"not null;default:'new'"`
	Priority                TicketPriority         `json:"priority" gorm:"not null;default:'normal'"`
	Urgency                 TicketUrgency          `json:"urgency" gorm:"not null;default:'medium'"`
	
	// Scheduling
	ScheduledDate           *time.Time             `json:"scheduled_date"`
	DueDate                 *time.Time             `json:"due_date"`
	EstimatedDurationMinutes int32                 `json:"estimated_duration_minutes"`
	ActualDurationMinutes   *int32                 `json:"actual_duration_minutes"`
	
	// Assignment
	AssignedTechnicianID    *int64                 `json:"assigned_technician_id" gorm:"index"`
	TeamMemberIDs           json.RawMessage        `json:"team_member_ids" gorm:"type:jsonb"`
	
	// Location
	ServiceAddress          string                 `json:"service_address" gorm:"size:500"`
	Latitude                *float64               `json:"latitude"`
	Longitude               *float64               `json:"longitude"`
	
	// Financial
	EstimatedCost           float64                `json:"estimated_cost" gorm:"default:0"`
	ActualCost              float64                `json:"actual_cost" gorm:"default:0"`
	LaborCost               float64                `json:"labor_cost" gorm:"default:0"`
	PartsCost               float64                `json:"parts_cost" gorm:"default:0"`
	
	// SLA & Performance
	SLADeadline             *time.Time             `json:"sla_deadline"`
	SLABreached             bool                   `json:"sla_breached" gorm:"default:false"`
	ResponseTimeMinutes     *int32                 `json:"response_time_minutes"`
	ResolutionTimeMinutes   *int32                 `json:"resolution_time_minutes"`
	
	// Customer Feedback
	CustomerRating          *int32                 `json:"customer_rating" gorm:"check:customer_rating >= 1 AND customer_rating <= 5"`
	CustomerFeedback        string                 `json:"customer_feedback" gorm:"type:text"`
	
	// Metadata
	CustomFields            json.RawMessage        `json:"custom_fields" gorm:"type:jsonb"`
	Tags                    json.RawMessage        `json:"tags" gorm:"type:jsonb"`
	
	// Timestamps
	CreatedAt               time.Time              `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt               time.Time              `json:"updated_at" gorm:"autoUpdateTime"`
	CompletedAt             *time.Time             `json:"completed_at"`
	ClosedAt                *time.Time             `json:"closed_at"`
	
	// Relationships
	Comments                []TicketComment        `json:"comments,omitempty" gorm:"foreignKey:TicketID"`
	Attachments             []TicketAttachment     `json:"attachments,omitempty" gorm:"foreignKey:TicketID"`
	Resources               []TicketResource       `json:"resources,omitempty" gorm:"foreignKey:TicketID"`
	StatusHistory           []TicketStatusHistory  `json:"status_history,omitempty" gorm:"foreignKey:TicketID"`
}

// TicketType represents the type of service ticket
type TicketType string

const (
	TicketTypeInstallation  TicketType = "installation"
	TicketTypeMaintenance   TicketType = "maintenance"
	TicketTypeRepair        TicketType = "repair"
	TicketTypeInspection    TicketType = "inspection"
	TicketTypeEmergency     TicketType = "emergency"
	TicketTypeConsultation  TicketType = "consultation"
	TicketTypeUpgrade       TicketType = "upgrade"
	TicketTypeRemoval       TicketType = "removal"
)

// TicketStatus represents the current status of a ticket
type TicketStatus string

const (
	TicketStatusNew              TicketStatus = "new"
	TicketStatusAssigned         TicketStatus = "assigned"
	TicketStatusInProgress       TicketStatus = "in_progress"
	TicketStatusOnHold           TicketStatus = "on_hold"
	TicketStatusWaitingParts     TicketStatus = "waiting_parts"
	TicketStatusWaitingCustomer  TicketStatus = "waiting_customer"
	TicketStatusCompleted        TicketStatus = "completed"
	TicketStatusClosed           TicketStatus = "closed"
	TicketStatusCancelled        TicketStatus = "cancelled"
)

// TicketPriority represents the priority level of a ticket
type TicketPriority string

const (
	TicketPriorityLow      TicketPriority = "low"
	TicketPriorityNormal   TicketPriority = "normal"
	TicketPriorityHigh     TicketPriority = "high"
	TicketPriorityCritical TicketPriority = "critical"
)

// TicketUrgency represents the urgency level of a ticket
type TicketUrgency string

const (
	TicketUrgencyLow       TicketUrgency = "low"
	TicketUrgencyMedium    TicketUrgency = "medium"
	TicketUrgencyHigh      TicketUrgency = "high"
	TicketUrgencyEmergency TicketUrgency = "emergency"
)

// TicketComment represents a comment on a ticket
type TicketComment struct {
	ID                int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	TicketID          int64     `json:"ticket_id" gorm:"not null;index"`
	AuthorID          int64     `json:"author_id" gorm:"not null"`
	AuthorName        string    `json:"author_name" gorm:"size:255"`
	AuthorType        string    `json:"author_type" gorm:"size:50"` // technician, customer, system
	Content           string    `json:"content" gorm:"type:text;not null"`
	IsInternal        bool      `json:"is_internal" gorm:"default:false"`
	IsSystemGenerated bool      `json:"is_system_generated" gorm:"default:false"`
	CreatedAt         time.Time `json:"created_at" gorm:"autoCreateTime"`
	
	// Relationships
	Ticket            *ServiceTicket `json:"ticket,omitempty" gorm:"foreignKey:TicketID"`
}

// TicketAttachment represents a file attachment on a ticket
type TicketAttachment struct {
	ID          int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	TicketID    int64     `json:"ticket_id" gorm:"not null;index"`
	Filename    string    `json:"filename" gorm:"not null;size:255"`
	FileType    string    `json:"file_type" gorm:"size:100"`
	FileSize    int64     `json:"file_size"`
	FileURL     string    `json:"file_url" gorm:"size:500"`
	Description string    `json:"description" gorm:"type:text"`
	UploadedBy  string    `json:"uploaded_by" gorm:"size:255"`
	UploadedAt  time.Time `json:"uploaded_at" gorm:"autoCreateTime"`
	
	// Relationships
	Ticket      *ServiceTicket `json:"ticket,omitempty" gorm:"foreignKey:TicketID"`
}

// TicketResource represents a resource (part, tool, material) used in a ticket
type TicketResource struct {
	ID              int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	TicketID        int64     `json:"ticket_id" gorm:"not null;index"`
	ResourceType    string    `json:"resource_type" gorm:"not null;size:50"` // part, tool, material
	ResourceName    string    `json:"resource_name" gorm:"not null;size:255"`
	PartNumber      string    `json:"part_number" gorm:"size:100"`
	QuantityPlanned int32     `json:"quantity_planned" gorm:"default:0"`
	QuantityUsed    int32     `json:"quantity_used" gorm:"default:0"`
	UnitCost        float64   `json:"unit_cost" gorm:"default:0"`
	TotalCost       float64   `json:"total_cost" gorm:"default:0"`
	Supplier        string    `json:"supplier" gorm:"size:255"`
	Notes           string    `json:"notes" gorm:"type:text"`
	AddedAt         time.Time `json:"added_at" gorm:"autoCreateTime"`
	
	// Relationships
	Ticket          *ServiceTicket `json:"ticket,omitempty" gorm:"foreignKey:TicketID"`
}

// TicketStatusHistory represents the history of status changes for a ticket
type TicketStatusHistory struct {
	ID           int64        `json:"id" gorm:"primaryKey;autoIncrement"`
	TicketID     int64        `json:"ticket_id" gorm:"not null;index"`
	FromStatus   TicketStatus `json:"from_status"`
	ToStatus     TicketStatus `json:"to_status" gorm:"not null"`
	ChangedBy    int64        `json:"changed_by" gorm:"not null"`
	ChangeReason string       `json:"change_reason" gorm:"type:text"`
	ChangedAt    time.Time    `json:"changed_at" gorm:"autoCreateTime"`
	
	// Relationships
	Ticket       *ServiceTicket `json:"ticket,omitempty" gorm:"foreignKey:TicketID"`
}

// TableName returns the table name for ServiceTicket
func (ServiceTicket) TableName() string {
	return "service_tickets"
}

// TableName returns the table name for TicketComment
func (TicketComment) TableName() string {
	return "ticket_comments"
}

// TableName returns the table name for TicketAttachment
func (TicketAttachment) TableName() string {
	return "ticket_attachments"
}

// TableName returns the table name for TicketResource
func (TicketResource) TableName() string {
	return "ticket_resources"
}

// TableName returns the table name for TicketStatusHistory
func (TicketStatusHistory) TableName() string {
	return "ticket_status_history"
}

// GenerateTicketNumber generates a unique ticket number
func (st *ServiceTicket) GenerateTicketNumber() {
	if st.TicketNumber == "" {
		// Format: TKT-YYYYMMDD-HHMMSS-ID
		now := time.Now()
		st.TicketNumber = fmt.Sprintf("TKT-%s-%d", 
			now.Format("20060102-150405"), 
			now.UnixNano()%10000)
	}
}

// IsOverdue checks if the ticket is overdue
func (st *ServiceTicket) IsOverdue() bool {
	if st.DueDate == nil {
		return false
	}
	return time.Now().After(*st.DueDate) && 
		st.Status != TicketStatusCompleted && 
		st.Status != TicketStatusClosed && 
		st.Status != TicketStatusCancelled
}

// IsSLABreached checks if SLA has been breached
func (st *ServiceTicket) IsSLABreached() bool {
	if st.SLADeadline == nil {
		return false
	}
	return time.Now().After(*st.SLADeadline) && 
		st.Status != TicketStatusCompleted && 
		st.Status != TicketStatusClosed
}

// CanTransitionTo checks if ticket can transition to a new status
func (st *ServiceTicket) CanTransitionTo(newStatus TicketStatus) bool {
	validTransitions := map[TicketStatus][]TicketStatus{
		TicketStatusNew: {
			TicketStatusAssigned, 
			TicketStatusCancelled,
		},
		TicketStatusAssigned: {
			TicketStatusInProgress, 
			TicketStatusOnHold, 
			TicketStatusCancelled,
		},
		TicketStatusInProgress: {
			TicketStatusOnHold, 
			TicketStatusWaitingParts, 
			TicketStatusWaitingCustomer, 
			TicketStatusCompleted,
		},
		TicketStatusOnHold: {
			TicketStatusInProgress, 
			TicketStatusCancelled,
		},
		TicketStatusWaitingParts: {
			TicketStatusInProgress, 
			TicketStatusOnHold,
		},
		TicketStatusWaitingCustomer: {
			TicketStatusInProgress, 
			TicketStatusOnHold,
		},
		TicketStatusCompleted: {
			TicketStatusClosed,
		},
	}
	
	allowedStatuses, exists := validTransitions[st.Status]
	if !exists {
		return false
	}
	
	for _, status := range allowedStatuses {
		if status == newStatus {
			return true
		}
	}
	
	return false
}

// GetTotalCost calculates the total cost of the ticket
func (st *ServiceTicket) GetTotalCost() float64 {
	return st.LaborCost + st.PartsCost
}

// GetDurationHours returns the duration in hours
func (st *ServiceTicket) GetDurationHours() float64 {
	if st.ActualDurationMinutes != nil {
		return float64(*st.ActualDurationMinutes) / 60.0
	}
	return float64(st.EstimatedDurationMinutes) / 60.0
}
