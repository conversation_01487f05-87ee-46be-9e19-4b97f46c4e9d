package biz

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockCustomerRepo is a mock implementation of CustomerRepo
type MockCustomerRepo struct {
	mock.Mock
}

func (m *MockCustomerRepo) CreateCustomer(ctx context.Context, customer *Customer) (*Customer, error) {
	args := m.Called(ctx, customer)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*Customer), args.Error(1)
}

func (m *MockCustomerRepo) GetCustomer(ctx context.Context, id int64) (*Customer, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*Customer), args.Error(1)
}

func (m *MockCustomerRepo) ListCustomers(ctx context.Context, page, pageSize int32, searchQuery, emailFilter, phoneFilter string) ([]*Customer, int32, error) {
	args := m.Called(ctx, page, pageSize, searchQuery, emailFilter, phoneFilter)
	if args.Get(0) == nil {
		return nil, args.Get(1).(int32), args.Error(2)
	}
	return args.Get(0).([]*Customer), args.Get(1).(int32), args.Error(2)
}

func (m *MockCustomerRepo) UpdateCustomer(ctx context.Context, customer *Customer) (*Customer, error) {
	args := m.Called(ctx, customer)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*Customer), args.Error(1)
}

func (m *MockCustomerRepo) DeleteCustomer(ctx context.Context, id int64) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func TestNewCustomerUsecase(t *testing.T) {
	mockRepo := new(MockCustomerRepo)
	uc := NewCustomerUsecase(mockRepo, log.DefaultLogger)
	assert.NotNil(t, uc)
	assert.Equal(t, mockRepo, uc.repo)
}

func TestCreateCustomer(t *testing.T) {
	mockRepo := new(MockCustomerRepo)
	uc := NewCustomerUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()

	// Success case
	customer := &Customer{Name: "John Doe", Email: "<EMAIL>"}
	expectedCustomer := &Customer{ID: 1, Name: "John Doe", Email: "<EMAIL>", CreatedAt: time.Now(), UpdatedAt: time.Now()}
	mockRepo.On("CreateCustomer", ctx, mock.AnythingOfType("*biz.Customer")).Return(expectedCustomer, nil).Once()

	createdCustomer, err := uc.CreateCustomer(ctx, customer)
	assert.NoError(t, err)
	assert.Equal(t, expectedCustomer.Name, createdCustomer.Name)
	assert.Equal(t, expectedCustomer.Email, createdCustomer.Email)
	assert.NotZero(t, createdCustomer.CreatedAt)
	assert.NotZero(t, createdCustomer.UpdatedAt)
	mockRepo.AssertExpectations(t)

	// Error case: Empty Name
	customer = &Customer{Name: "", Email: "<EMAIL>"}
	createdCustomer, err = uc.CreateCustomer(ctx, customer)
	assert.ErrorIs(t, err, ErrCustomerNameRequired)
	assert.Nil(t, createdCustomer)
	mockRepo.AssertNotCalled(t, "CreateCustomer")

	// Error case: Empty Email
	customer = &Customer{Name: "John Doe", Email: ""}
	createdCustomer, err = uc.CreateCustomer(ctx, customer)
	assert.ErrorIs(t, err, ErrCustomerEmailRequired)
	assert.Nil(t, createdCustomer)
	mockRepo.AssertNotCalled(t, "CreateCustomer")

	// Error case: Repo returns error
	customer = &Customer{Name: "Jane Doe", Email: "<EMAIL>"}
	mockRepo.On("CreateCustomer", ctx, mock.AnythingOfType("*biz.Customer")).Return(nil, errors.New("db error")).Once()
	createdCustomer, err = uc.CreateCustomer(ctx, customer)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "db error")
	assert.Nil(t, createdCustomer)
	mockRepo.AssertExpectations(t)
}

func TestGetCustomer(t *testing.T) {
	mockRepo := new(MockCustomerRepo)
	uc := NewCustomerUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()

	// Success case
	expectedCustomer := &Customer{ID: 1, Name: "John Doe"}
	mockRepo.On("GetCustomer", ctx, int64(1)).Return(expectedCustomer, nil).Once()
	customer, err := uc.GetCustomer(ctx, 1)
	assert.NoError(t, err)
	assert.Equal(t, expectedCustomer, customer)
	mockRepo.AssertExpectations(t)

	// Error case: Invalid ID
	customer, err = uc.GetCustomer(ctx, 0)
	assert.ErrorIs(t, err, ErrInvalidCustomerID)
	assert.Nil(t, customer)
	mockRepo.AssertNotCalled(t, "GetCustomer")

	// Error case: Repo returns error
	mockRepo.On("GetCustomer", ctx, int64(2)).Return(nil, errors.New("not found")).Once()
	customer, err = uc.GetCustomer(ctx, 2)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "not found")
	assert.Nil(t, customer)
	mockRepo.AssertExpectations(t)
}

func TestListCustomers(t *testing.T) {
	mockRepo := new(MockCustomerRepo)
	uc := NewCustomerUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()

	// Success case
	expectedCustomers := []*Customer{{ID: 1, Name: "John"}, {ID: 2, Name: "Jane"}}
	mockRepo.On("ListCustomers", ctx, int32(1), int32(20), "", "", "").Return(expectedCustomers, int32(2), nil).Once()
	customers, total, err := uc.ListCustomers(ctx, 1, 20, "", "", "")
	assert.NoError(t, err)
	assert.Equal(t, expectedCustomers, customers)
	assert.Equal(t, int32(2), total)
	mockRepo.AssertExpectations(t)

	// Test case: page <= 0, pageSize <= 0
	mockRepo.On("ListCustomers", ctx, int32(1), int32(20), "", "", "").Return(expectedCustomers, int32(2), nil).Once()
	customers, total, err = uc.ListCustomers(ctx, 0, 0, "", "", "")
	assert.NoError(t, err)
	assert.Equal(t, expectedCustomers, customers)
	assert.Equal(t, int32(2), total)
	mockRepo.AssertExpectations(t)

	// Test case: pageSize > 100
	mockRepo.On("ListCustomers", ctx, int32(1), int32(20), "", "", "").Return(expectedCustomers, int32(2), nil).Once()
	customers, total, err = uc.ListCustomers(ctx, 1, 101, "", "", "")
	assert.NoError(t, err)
	assert.Equal(t, expectedCustomers, customers)
	assert.Equal(t, int32(2), total)
	mockRepo.AssertExpectations(t)

	// Error case: Repo returns error
	mockRepo.On("ListCustomers", ctx, int32(1), int32(20), "", "", "").Return(nil, int32(0), errors.New("db error")).Once()
	customers, total, err = uc.ListCustomers(ctx, 1, 20, "", "", "")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "db error")
	assert.Nil(t, customers)
	assert.Equal(t, int32(0), total)
	mockRepo.AssertExpectations(t)
}

func TestUpdateCustomer(t *testing.T) {
	mockRepo := new(MockCustomerRepo)
	uc := NewCustomerUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()

	// Success case
	customerToUpdate := &Customer{ID: 1, Name: "John Updated", Email: "<EMAIL>"}
	expectedCustomer := &Customer{ID: 1, Name: "John Updated", Email: "<EMAIL>", UpdatedAt: time.Now()}
	mockRepo.On("UpdateCustomer", ctx, mock.AnythingOfType("*biz.Customer")).Return(expectedCustomer, nil).Once()

	updatedCustomer, err := uc.UpdateCustomer(ctx, customerToUpdate)
	assert.NoError(t, err)
	assert.Equal(t, expectedCustomer.Name, updatedCustomer.Name)
	assert.Equal(t, expectedCustomer.Email, updatedCustomer.Email)
	assert.NotZero(t, updatedCustomer.UpdatedAt)
	mockRepo.AssertExpectations(t)

	// Error case: Invalid ID
	customerToUpdate = &Customer{ID: 0, Name: "Invalid", Email: "<EMAIL>"}
	updatedCustomer, err = uc.UpdateCustomer(ctx, customerToUpdate)
	assert.ErrorIs(t, err, ErrInvalidCustomerID)
	assert.Nil(t, updatedCustomer)
	mockRepo.AssertNotCalled(t, "UpdateCustomer")

	// Error case: Repo returns error
	customerToUpdate = &Customer{ID: 2, Name: "Jane Updated", Email: "<EMAIL>"}
	mockRepo.On("UpdateCustomer", ctx, mock.AnythingOfType("*biz.Customer")).Return(nil, errors.New("db error")).Once()
	updatedCustomer, err = uc.UpdateCustomer(ctx, customerToUpdate)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "db error")
	assert.Nil(t, updatedCustomer)
	mockRepo.AssertExpectations(t)
}

func TestDeleteCustomer(t *testing.T) {
	mockRepo := new(MockCustomerRepo)
	uc := NewCustomerUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()

	// Success case
	mockRepo.On("DeleteCustomer", ctx, int64(1)).Return(nil).Once()
	err := uc.DeleteCustomer(ctx, 1)
	assert.NoError(t, err)
	mockRepo.AssertExpectations(t)

	// Error case: Repo returns error
	mockRepo.On("DeleteCustomer", ctx, int64(2)).Return(errors.New("db error")).Once()
	err = uc.DeleteCustomer(ctx, 2)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "db error")
	mockRepo.AssertExpectations(t)
}

// Benchmarks
func BenchmarkCreateCustomer(b *testing.B) {
	mockRepo := new(MockCustomerRepo)
	uc := NewCustomerUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()
	customer := &Customer{Name: "Benchmark Customer", Email: "<EMAIL>"}
	expectedCustomer := &Customer{ID: 1, Name: "Benchmark Customer", Email: "<EMAIL>", CreatedAt: time.Now(), UpdatedAt: time.Now()}

	mockRepo.On("CreateCustomer", ctx, mock.AnythingOfType("*biz.Customer")).Return(expectedCustomer, nil)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = uc.CreateCustomer(ctx, customer)
	}
}

func BenchmarkListCustomers(b *testing.B) {
	mockRepo := new(MockCustomerRepo)
	uc := NewCustomerUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()
	expectedCustomers := []*Customer{{ID: 1, Name: "John"}, {ID: 2, Name: "Jane"}}

	mockRepo.On("ListCustomers", ctx, int32(1), int32(20), "", "", "").Return(expectedCustomers, int32(2), nil)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _, _ = uc.ListCustomers(ctx, 1, 20, "", "", "")
	}
}
