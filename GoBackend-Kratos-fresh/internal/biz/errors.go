package biz

import (
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
)

// Customer errors
var (
	ErrCustomerNotFound      = errors.NotFound("CUSTOMER_NOT_FOUND", "customer not found")
	ErrCustomerNameRequired  = errors.BadRequest("CUSTOMER_NAME_REQUIRED", "customer name is required")
	ErrCustomerEmailRequired = errors.BadRequest("CUSTOMER_EMAIL_REQUIRED", "customer email is required")
	ErrInvalidCustomerID     = errors.BadRequest("INVALID_CUSTOMER_ID", "invalid customer ID")
)

// Job errors
var (
	ErrJobNotFound        = errors.NotFound("JOB_NOT_FOUND", "job not found")
	ErrJobTitleRequired   = errors.BadRequest("JOB_TITLE_REQUIRED", "job title is required")
	ErrInvalidJobID       = errors.BadRequest("INVALID_JOB_ID", "invalid job ID")
	ErrInvalidJobStatus   = errors.BadRequest("INVALID_JOB_STATUS", "invalid job status")
	ErrInvalidJobPriority = errors.BadRequest("INVALID_JOB_PRIORITY", "invalid job priority")
)

// AI errors
var (
	ErrAIModelNotFound    = errors.NotFound("AI_MODEL_NOT_FOUND", "AI model not found")
	ErrAIModelUnavailable = errors.ServiceUnavailable("AI_MODEL_UNAVAILABLE", "AI model is unavailable")
	ErrInvalidAIRequest   = errors.BadRequest("INVALID_AI_REQUEST", "invalid AI request")
	ErrAIProcessingFailed = errors.InternalServer("AI_PROCESSING_FAILED", "AI processing failed")
)

// Email errors
var (
	ErrEmailSendFailed       = errors.InternalServer("EMAIL_SEND_FAILED", "failed to send email")
	ErrInvalidEmailAddress   = errors.BadRequest("INVALID_EMAIL_ADDRESS", "invalid email address")
	ErrEmailNotFound         = errors.NotFound("EMAIL_NOT_FOUND", "email not found")
	ErrEmailRequired         = errors.BadRequest("EMAIL_REQUIRED", "email is required")
	ErrEmailSubjectRequired  = errors.BadRequest("EMAIL_SUBJECT_REQUIRED", "email subject is required")
	ErrEmailFromRequired     = errors.BadRequest("EMAIL_FROM_REQUIRED", "email from address is required")
	ErrInvalidEmailID        = errors.BadRequest("INVALID_EMAIL_ID", "invalid email ID")
	ErrEmailAnalysisNotFound = errors.NotFound("EMAIL_ANALYSIS_NOT_FOUND", "email analysis not found")
	ErrEmailAnalysisFailed   = errors.InternalServer("EMAIL_ANALYSIS_FAILED", "email analysis failed")
	ErrAttachmentNotFound    = errors.NotFound("ATTACHMENT_NOT_FOUND", "attachment not found")
)

// MCP errors
var (
	ErrMCPServerUnavailable = errors.ServiceUnavailable("MCP_SERVER_UNAVAILABLE", "MCP server is unavailable")
	ErrMCPToolNotFound      = errors.NotFound("MCP_TOOL_NOT_FOUND", "MCP tool not found")
	ErrMCPInvalidRequest    = errors.BadRequest("MCP_INVALID_REQUEST", "invalid MCP request")
)

// Lead errors
var (
	ErrLeadNotFound            = errors.NotFound("LEAD_NOT_FOUND", "lead not found")
	ErrLeadNameRequired        = errors.BadRequest("LEAD_NAME_REQUIRED", "lead name is required")
	ErrLeadContactRequired     = errors.BadRequest("LEAD_CONTACT_REQUIRED", "lead email or phone is required")
	ErrInvalidLeadID           = errors.BadRequest("INVALID_LEAD_ID", "invalid lead ID")
	ErrLeadAlreadyExists       = errors.Conflict("LEAD_ALREADY_EXISTS", "lead with this email or phone already exists")
	ErrLeadImportFailed        = errors.InternalServer("LEAD_IMPORT_FAILED", "lead import failed")
	ErrLeadExportFailed        = errors.InternalServer("LEAD_EXPORT_FAILED", "lead export failed")
	ErrLeadDeduplicationFailed = errors.InternalServer("LEAD_DEDUPLICATION_FAILED", "lead deduplication failed")
	ErrLeadMergeFailed         = errors.InternalServer("LEAD_MERGE_FAILED", "lead merge failed")
)

// Campaign errors
var (
	ErrCampaignNotFound          = errors.NotFound("CAMPAIGN_NOT_FOUND", "campaign not found")
	ErrCampaignNameRequired      = errors.BadRequest("CAMPAIGN_NAME_REQUIRED", "campaign name is required")
	ErrInvalidCampaignID         = errors.BadRequest("INVALID_CAMPAIGN_ID", "invalid campaign ID")
	ErrCampaignAttributionFailed = errors.InternalServer("CAMPAIGN_ATTRIBUTION_FAILED", "campaign attribution failed")
)

// Equipment errors
var (
	ErrEquipmentNameRequired   = errors.BadRequest("EQUIPMENT_NAME_REQUIRED", "equipment name is required")
	ErrEquipmentTypeRequired   = errors.BadRequest("EQUIPMENT_TYPE_REQUIRED", "equipment type is required")
	ErrEquipmentSerialRequired = errors.BadRequest("EQUIPMENT_SERIAL_REQUIRED", "equipment serial number is required")
	ErrInvalidEquipmentID      = errors.BadRequest("INVALID_EQUIPMENT_ID", "invalid equipment ID")
	ErrEquipmentNotFound       = errors.NotFound("EQUIPMENT_NOT_FOUND", "equipment not found")
	ErrMaintenanceTypeRequired = errors.BadRequest("MAINTENANCE_TYPE_REQUIRED", "maintenance type is required")
	ErrMaintenanceDateRequired = errors.BadRequest("MAINTENANCE_DATE_REQUIRED", "maintenance date is required")
)

// Service Ticket errors
var (
	ErrTicketTitleRequired     = errors.BadRequest("TICKET_TITLE_REQUIRED", "ticket title is required")
	ErrTicketTypeRequired      = errors.BadRequest("TICKET_TYPE_REQUIRED", "ticket type is required")
	ErrInvalidTicketID         = errors.BadRequest("INVALID_TICKET_ID", "invalid ticket ID")
	ErrTicketNotFound          = errors.NotFound("TICKET_NOT_FOUND", "ticket not found")
	ErrInvalidTechnicianID     = errors.BadRequest("INVALID_TECHNICIAN_ID", "invalid technician ID")
	ErrInvalidStatusTransition = errors.BadRequest("INVALID_STATUS_TRANSITION", "invalid status transition")
	ErrTicketCannotBeAssigned  = errors.BadRequest("TICKET_CANNOT_BE_ASSIGNED", "ticket cannot be assigned in current status")
	ErrCommentContentRequired  = errors.BadRequest("COMMENT_CONTENT_REQUIRED", "comment content is required")
	ErrAttachmentRequired      = errors.BadRequest("ATTACHMENT_REQUIRED", "attachment file is required")
	ErrResourceNameRequired    = errors.BadRequest("RESOURCE_NAME_REQUIRED", "resource name is required")
)

// 🔧 Enhanced Error Handling - CustomError represents a comprehensive error structure
// 🔧 Enhanced Error Handling - CustomError represents a comprehensive error structure
type CustomError struct {
	Code       int            `json:"code"`
	Message    string         `json:"message"`
	Data       any            `json:"data,omitempty"`
	Timestamp  time.Time      `json:"timestamp"`
	RequestID  string         `json:"request_id,omitempty"`
	Context    map[string]any `json:"context,omitempty"`
	Stack      string         `json:"stack,omitempty"`
	wrappedErr error          // New field to hold the wrapped error
}

// Error implements the error interface
func (e *CustomError) Error() string {
	if e.wrappedErr != nil {
		return fmt.Sprintf("code: %d, message: %s, data: %+v, wrapped: %v", e.Code, e.Message, e.Data, e.wrappedErr)
	}
	return fmt.Sprintf("code: %d, message: %s, data: %+v", e.Code, e.Message, e.Data)
}

// Unwrap returns the wrapped error, allowing errors.Is and errors.As to work.
func (e *CustomError) Unwrap() error {
	return e.wrappedErr
}

// WithContext adds context information to the error
func (e *CustomError) WithContext(key string, value any) *CustomError {
	if e.Context == nil {
		e.Context = make(map[string]any)
	}
	e.Context[key] = value
	return e
}

// WithRequestID adds request ID to the error
func (e *CustomError) WithRequestID(requestID string) *CustomError {
	e.RequestID = requestID
	return e
}

// NewCustomError creates a new CustomError with timestamp
// NewCustomError creates a new CustomError with timestamp and an optional wrapped error.
func NewCustomError(code int, message string, data any, wrappedErr ...error) *CustomError {
	err := &CustomError{
		Code:      code,
		Message:   message,
		Data:      data,
		Timestamp: time.Now(),
	}
	if len(wrappedErr) > 0 && wrappedErr[0] != nil {
		err.wrappedErr = wrappedErr[0]
	}
	return err
}

// Common error codes for tRPC
const (
	// Standard JSON-RPC 2.0 error codes
	ErrCodeParseError     = -32700
	ErrCodeInvalidRequest = -32600
	ErrCodeMethodNotFound = -32601
	ErrCodeInvalidParams  = -32602
	ErrCodeInternalError  = -32603

	// Custom application error codes
	ErrCodeValidationFailed   = -32000
	ErrCodeUnauthorized       = -32001
	ErrCodeForbidden          = -32002
	ErrCodeNotFound           = -32003
	ErrCodeConflict           = -32004
	ErrCodeRateLimit          = -32005
	ErrCodeServiceUnavailable = -32006
	ErrCodeCircuitBreakerOpen = -32007
)

// Predefined common errors
var (
	ErrParseError     = NewCustomError(ErrCodeParseError, "Parse error", "Invalid JSON was received by the server")
	ErrInvalidRequest = NewCustomError(ErrCodeInvalidRequest, "Invalid Request", "The JSON sent is not a valid Request object")
	ErrMethodNotFound = NewCustomError(ErrCodeMethodNotFound, "Method not found", "The method does not exist / is not available")
	ErrInvalidParams  = NewCustomError(ErrCodeInvalidParams, "Invalid params", "Invalid method parameter(s)")
	ErrInternalError  = NewCustomError(ErrCodeInternalError, "Internal error", "Internal JSON-RPC error")
)
