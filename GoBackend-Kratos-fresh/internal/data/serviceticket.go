package data

import (
	"context"
	"encoding/json"
	"time"

	"gobackend-hvac-kratos/internal/biz"
	"gobackend-hvac-kratos/internal/entity"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
)

// 🎫 Service Ticket Repository - Data Layer for Ticket Management
// GoBackend-Kratos HVAC CRM System

// serviceTicketRepo implements the service ticket repository
type serviceTicketRepo struct {
	data *Data
	log  *log.Helper
}

// NewServiceTicketRepo creates a new service ticket repository
func NewServiceTicketRepo(data *Data, logger log.Logger) biz.ServiceTicketRepo {
	return &serviceTicketRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

// ============================================================================
// TICKET CRUD OPERATIONS
// ============================================================================

// CreateTicket creates a new service ticket
func (r *serviceTicketRepo) CreateTicket(ctx context.Context, ticket *entity.ServiceTicket) (*entity.ServiceTicket, error) {
	r.log.WithContext(ctx).Infof("Creating service ticket in database: %s", ticket.Title)

	if err := r.data.db.WithContext(ctx).Create(ticket).Error; err != nil {
		r.log.WithContext(ctx).Errorf("Failed to create service ticket: %v", err)
		return nil, err
	}

	return ticket, nil
}

// GetTicket retrieves a service ticket by ID
func (r *serviceTicketRepo) GetTicket(ctx context.Context, id int64, includeRelations bool) (*entity.ServiceTicket, error) {
	r.log.WithContext(ctx).Infof("Getting service ticket from database: %d", id)

	var ticket entity.ServiceTicket
	query := r.data.db.WithContext(ctx)

	if includeRelations {
		query = query.
			Preload("Comments").
			Preload("Attachments").
			Preload("Resources").
			Preload("StatusHistory")
	}

	err := query.First(&ticket, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, biz.ErrTicketNotFound
		}
		r.log.WithContext(ctx).Errorf("Failed to get service ticket: %v", err)
		return nil, err
	}

	return &ticket, nil
}

// ListTickets retrieves tickets with filtering and pagination
func (r *serviceTicketRepo) ListTickets(ctx context.Context, filters biz.TicketFilters) ([]*entity.ServiceTicket, int32, error) {
	r.log.WithContext(ctx).Infof("Listing service tickets from database: page=%d, size=%d", filters.Page, filters.PageSize)

	var tickets []*entity.ServiceTicket
	var total int64

	query := r.data.db.WithContext(ctx).Model(&entity.ServiceTicket{})

	// Apply filters
	if filters.CustomerID > 0 {
		query = query.Where("customer_id = ?", filters.CustomerID)
	}
	if filters.TechnicianID > 0 {
		query = query.Where("assigned_technician_id = ?", filters.TechnicianID)
	}
	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}
	if filters.Priority != "" {
		query = query.Where("priority = ?", filters.Priority)
	}
	if filters.Type != "" {
		query = query.Where("type = ?", filters.Type)
	}
	if filters.DateFrom != nil {
		query = query.Where("created_at >= ?", *filters.DateFrom)
	}
	if filters.DateTo != nil {
		query = query.Where("created_at <= ?", *filters.DateTo)
	}
	if filters.SearchQuery != "" {
		searchPattern := "%" + filters.SearchQuery + "%"
		query = query.Where("title ILIKE ? OR description ILIKE ? OR ticket_number ILIKE ?",
			searchPattern, searchPattern, searchPattern)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		r.log.WithContext(ctx).Errorf("Failed to count service tickets: %v", err)
		return nil, 0, err
	}

	// Apply sorting
	sortBy := "created_at"
	if filters.SortBy != "" {
		sortBy = filters.SortBy
	}
	sortOrder := "DESC"
	if filters.SortOrder == "asc" {
		sortOrder = "ASC"
	}

	// Apply pagination and get results
	offset := (filters.Page - 1) * filters.PageSize
	err := query.
		Offset(int(offset)).
		Limit(int(filters.PageSize)).
		Order(sortBy + " " + sortOrder).
		Find(&tickets).Error

	if err != nil {
		r.log.WithContext(ctx).Errorf("Failed to list service tickets: %v", err)
		return nil, 0, err
	}

	return tickets, int32(total), nil
}

// UpdateTicket updates an existing service ticket
func (r *serviceTicketRepo) UpdateTicket(ctx context.Context, ticket *entity.ServiceTicket) (*entity.ServiceTicket, error) {
	r.log.WithContext(ctx).Infof("Updating service ticket in database: %d", ticket.ID)

	if err := r.data.db.WithContext(ctx).Save(ticket).Error; err != nil {
		r.log.WithContext(ctx).Errorf("Failed to update service ticket: %v", err)
		return nil, err
	}

	return ticket, nil
}

// DeleteTicket deletes a service ticket by ID
func (r *serviceTicketRepo) DeleteTicket(ctx context.Context, id int64) error {
	r.log.WithContext(ctx).Infof("Deleting service ticket from database: %d", id)

	result := r.data.db.WithContext(ctx).Delete(&entity.ServiceTicket{}, id)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("Failed to delete service ticket: %v", result.Error)
		return result.Error
	}

	if result.RowsAffected == 0 {
		return biz.ErrTicketNotFound
	}

	return nil
}

// ============================================================================
// STATUS MANAGEMENT
// ============================================================================

// UpdateTicketStatus updates ticket status and creates history record
func (r *serviceTicketRepo) UpdateTicketStatus(ctx context.Context, ticketID int64, newStatus entity.TicketStatus, changedBy int64, reason string) (*entity.ServiceTicket, *entity.TicketStatusHistory, error) {
	r.log.WithContext(ctx).Infof("Updating ticket status in database: %d to %s", ticketID, newStatus)

	// Start transaction
	tx := r.data.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Get current ticket
	var ticket entity.ServiceTicket
	if err := tx.First(&ticket, ticketID).Error; err != nil {
		tx.Rollback()
		if err == gorm.ErrRecordNotFound {
			return nil, nil, biz.ErrTicketNotFound
		}
		return nil, nil, err
	}

	oldStatus := ticket.Status

	// Update ticket status
	ticket.Status = newStatus
	ticket.UpdatedAt = time.Now()

	// Set completion/closure timestamps
	now := time.Now()
	if newStatus == entity.TicketStatusCompleted && ticket.CompletedAt == nil {
		ticket.CompletedAt = &now
	}
	if newStatus == entity.TicketStatusClosed && ticket.ClosedAt == nil {
		ticket.ClosedAt = &now
	}

	if err := tx.Save(&ticket).Error; err != nil {
		tx.Rollback()
		return nil, nil, err
	}

	// Create status history record
	statusHistory := &entity.TicketStatusHistory{
		TicketID:     ticketID,
		FromStatus:   oldStatus,
		ToStatus:     newStatus,
		ChangedBy:    changedBy,
		ChangeReason: reason,
		ChangedAt:    now,
	}

	if err := tx.Create(statusHistory).Error; err != nil {
		tx.Rollback()
		return nil, nil, err
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return nil, nil, err
	}

	return &ticket, statusHistory, nil
}

// GetTicketHistory retrieves status change history for a ticket
func (r *serviceTicketRepo) GetTicketHistory(ctx context.Context, ticketID int64, page, pageSize int32) ([]*entity.TicketStatusHistory, int32, error) {
	r.log.WithContext(ctx).Infof("Getting ticket history from database: %d", ticketID)

	var history []*entity.TicketStatusHistory
	var total int64

	query := r.data.db.WithContext(ctx).
		Model(&entity.TicketStatusHistory{}).
		Where("ticket_id = ?", ticketID)

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		r.log.WithContext(ctx).Errorf("Failed to count ticket history: %v", err)
		return nil, 0, err
	}

	// Apply pagination and get results
	offset := (page - 1) * pageSize
	err := query.
		Offset(int(offset)).
		Limit(int(pageSize)).
		Order("changed_at DESC").
		Find(&history).Error

	if err != nil {
		r.log.WithContext(ctx).Errorf("Failed to get ticket history: %v", err)
		return nil, 0, err
	}

	return history, int32(total), nil
}

// ============================================================================
// ASSIGNMENT MANAGEMENT
// ============================================================================

// AssignTicket assigns a ticket to a technician and team
func (r *serviceTicketRepo) AssignTicket(ctx context.Context, ticketID, technicianID int64, teamMemberIDs []int64, assignedBy int64) (*entity.ServiceTicket, error) {
	r.log.WithContext(ctx).Infof("Assigning ticket in database: %d to technician %d", ticketID, technicianID)

	// Get current ticket
	var ticket entity.ServiceTicket
	if err := r.data.db.WithContext(ctx).First(&ticket, ticketID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, biz.ErrTicketNotFound
		}
		return nil, err
	}

	// Update assignment
	ticket.AssignedTechnicianID = &technicianID
	ticket.Status = entity.TicketStatusAssigned
	ticket.UpdatedAt = time.Now()

	// Convert team member IDs to JSON
	if len(teamMemberIDs) > 0 {
		teamJSON, err := json.Marshal(teamMemberIDs)
		if err != nil {
			return nil, err
		}
		ticket.TeamMemberIDs = teamJSON
	}

	if err := r.data.db.WithContext(ctx).Save(&ticket).Error; err != nil {
		r.log.WithContext(ctx).Errorf("Failed to assign ticket: %v", err)
		return nil, err
	}

	// Create status history record
	statusHistory := &entity.TicketStatusHistory{
		TicketID:     ticketID,
		FromStatus:   entity.TicketStatusNew,
		ToStatus:     entity.TicketStatusAssigned,
		ChangedBy:    assignedBy,
		ChangeReason: "Ticket assigned to technician",
		ChangedAt:    time.Now(),
	}

	if err := r.data.db.WithContext(ctx).Create(statusHistory).Error; err != nil {
		r.log.WithContext(ctx).Errorf("Failed to create assignment history: %v", err)
		// Don't fail the assignment if history creation fails
	}

	return &ticket, nil
}

// GetTechnicianTickets retrieves tickets assigned to a technician
func (r *serviceTicketRepo) GetTechnicianTickets(ctx context.Context, technicianID int64, filters biz.TechnicianTicketFilters) ([]*entity.ServiceTicket, int32, error) {
	r.log.WithContext(ctx).Infof("Getting technician tickets from database: %d", technicianID)

	var tickets []*entity.ServiceTicket
	var total int64

	query := r.data.db.WithContext(ctx).
		Model(&entity.ServiceTicket{}).
		Where("assigned_technician_id = ?", technicianID)

	// Apply filters
	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}
	if filters.DateFrom != nil {
		query = query.Where("created_at >= ?", *filters.DateFrom)
	}
	if filters.DateTo != nil {
		query = query.Where("created_at <= ?", *filters.DateTo)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		r.log.WithContext(ctx).Errorf("Failed to count technician tickets: %v", err)
		return nil, 0, err
	}

	// Apply pagination and get results
	offset := (filters.Page - 1) * filters.PageSize
	err := query.
		Offset(int(offset)).
		Limit(int(filters.PageSize)).
		Order("created_at DESC").
		Find(&tickets).Error

	if err != nil {
		r.log.WithContext(ctx).Errorf("Failed to get technician tickets: %v", err)
		return nil, 0, err
	}

	return tickets, int32(total), nil
}

// ============================================================================
// COMMUNICATION MANAGEMENT
// ============================================================================

// AddComment adds a comment to a ticket
func (r *serviceTicketRepo) AddComment(ctx context.Context, comment *entity.TicketComment) (*entity.TicketComment, error) {
	r.log.WithContext(ctx).Infof("Adding comment to ticket in database: %d", comment.TicketID)

	if err := r.data.db.WithContext(ctx).Create(comment).Error; err != nil {
		r.log.WithContext(ctx).Errorf("Failed to add comment: %v", err)
		return nil, err
	}

	return comment, nil
}

// GetComments retrieves comments for a ticket
func (r *serviceTicketRepo) GetComments(ctx context.Context, ticketID int64, page, pageSize int32, includeInternal bool) ([]*entity.TicketComment, int32, error) {
	r.log.WithContext(ctx).Infof("Getting comments from database: ticket=%d", ticketID)

	var comments []*entity.TicketComment
	var total int64

	query := r.data.db.WithContext(ctx).
		Model(&entity.TicketComment{}).
		Where("ticket_id = ?", ticketID)

	if !includeInternal {
		query = query.Where("is_internal = ?", false)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		r.log.WithContext(ctx).Errorf("Failed to count comments: %v", err)
		return nil, 0, err
	}

	// Apply pagination and get results
	offset := (page - 1) * pageSize
	err := query.
		Offset(int(offset)).
		Limit(int(pageSize)).
		Order("created_at ASC").
		Find(&comments).Error

	if err != nil {
		r.log.WithContext(ctx).Errorf("Failed to get comments: %v", err)
		return nil, 0, err
	}

	return comments, int32(total), nil
}

// ============================================================================
// ATTACHMENT MANAGEMENT
// ============================================================================

// AddAttachment adds an attachment to a ticket
func (r *serviceTicketRepo) AddAttachment(ctx context.Context, attachment *entity.TicketAttachment) (*entity.TicketAttachment, error) {
	r.log.WithContext(ctx).Infof("Adding attachment to ticket in database: %d", attachment.TicketID)

	if err := r.data.db.WithContext(ctx).Create(attachment).Error; err != nil {
		r.log.WithContext(ctx).Errorf("Failed to add attachment: %v", err)
		return nil, err
	}

	return attachment, nil
}

// GetAttachments retrieves attachments for a ticket
func (r *serviceTicketRepo) GetAttachments(ctx context.Context, ticketID int64) ([]*entity.TicketAttachment, error) {
	r.log.WithContext(ctx).Infof("Getting attachments from database: ticket=%d", ticketID)

	var attachments []*entity.TicketAttachment
	err := r.data.db.WithContext(ctx).
		Where("ticket_id = ?", ticketID).
		Order("uploaded_at DESC").
		Find(&attachments).Error

	if err != nil {
		r.log.WithContext(ctx).Errorf("Failed to get attachments: %v", err)
		return nil, err
	}

	return attachments, nil
}

// ============================================================================
// RESOURCE MANAGEMENT
// ============================================================================

// AddResource adds a resource to a ticket
func (r *serviceTicketRepo) AddResource(ctx context.Context, resource *entity.TicketResource) (*entity.TicketResource, error) {
	r.log.WithContext(ctx).Infof("Adding resource to ticket in database: %d", resource.TicketID)

	if err := r.data.db.WithContext(ctx).Create(resource).Error; err != nil {
		r.log.WithContext(ctx).Errorf("Failed to add resource: %v", err)
		return nil, err
	}

	return resource, nil
}

// GetResources retrieves resources for a ticket
func (r *serviceTicketRepo) GetResources(ctx context.Context, ticketID int64) ([]*entity.TicketResource, error) {
	r.log.WithContext(ctx).Infof("Getting resources from database: ticket=%d", ticketID)

	var resources []*entity.TicketResource
	err := r.data.db.WithContext(ctx).
		Where("ticket_id = ?", ticketID).
		Order("added_at DESC").
		Find(&resources).Error

	if err != nil {
		r.log.WithContext(ctx).Errorf("Failed to get resources: %v", err)
		return nil, err
	}

	return resources, nil
}

// UpdateResourceUsage updates resource usage information
func (r *serviceTicketRepo) UpdateResourceUsage(ctx context.Context, ticketID, resourceID int64, quantityUsed int32, actualCost float64, notes string) (*entity.TicketResource, error) {
	r.log.WithContext(ctx).Infof("Updating resource usage in database: ticket=%d, resource=%d", ticketID, resourceID)

	var resource entity.TicketResource
	err := r.data.db.WithContext(ctx).
		Where("id = ? AND ticket_id = ?", resourceID, ticketID).
		First(&resource).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, biz.ErrTicketNotFound
		}
		return nil, err
	}

	// Update resource usage
	resource.QuantityUsed = quantityUsed
	resource.TotalCost = float64(quantityUsed) * actualCost
	if notes != "" {
		resource.Notes = notes
	}

	if err := r.data.db.WithContext(ctx).Save(&resource).Error; err != nil {
		r.log.WithContext(ctx).Errorf("Failed to update resource usage: %v", err)
		return nil, err
	}

	return &resource, nil
}

// ============================================================================
// ANALYTICS AND REPORTING
// ============================================================================

// GetTicketAnalytics retrieves comprehensive ticket analytics
func (r *serviceTicketRepo) GetTicketAnalytics(ctx context.Context, filters biz.AnalyticsFilters) (*biz.TicketAnalytics, error) {
	r.log.WithContext(ctx).Info("Getting ticket analytics from database")

	analytics := &biz.TicketAnalytics{}

	query := r.data.db.WithContext(ctx).Model(&entity.ServiceTicket{}).
		Where("created_at BETWEEN ? AND ?", filters.DateFrom, filters.DateTo)

	// Apply optional filters
	if filters.TechnicianID != nil {
		query = query.Where("assigned_technician_id = ?", *filters.TechnicianID)
	}
	if filters.CustomerID != nil {
		query = query.Where("customer_id = ?", *filters.CustomerID)
	}
	if filters.Type != nil {
		query = query.Where("type = ?", *filters.Type)
	}

	// Get basic counts
	var totalTickets int64
	query.Count(&totalTickets)
	analytics.TotalTickets = int32(totalTickets)

	var completedTickets int64
	r.data.db.WithContext(ctx).Model(&entity.ServiceTicket{}).
		Where("created_at BETWEEN ? AND ? AND status IN ?", filters.DateFrom, filters.DateTo,
			[]entity.TicketStatus{entity.TicketStatusCompleted, entity.TicketStatusClosed}).
		Count(&completedTickets)
	analytics.CompletedTickets = int32(completedTickets)

	var overdueTickets int64
	r.data.db.WithContext(ctx).Model(&entity.ServiceTicket{}).
		Where("created_at BETWEEN ? AND ? AND due_date < ? AND status NOT IN ?",
			filters.DateFrom, filters.DateTo, time.Now(),
			[]entity.TicketStatus{entity.TicketStatusCompleted, entity.TicketStatusClosed}).
		Count(&overdueTickets)
	analytics.OverdueTickets = int32(overdueTickets)

	// Calculate rates
	if analytics.TotalTickets > 0 {
		analytics.CompletionRate = float64(analytics.CompletedTickets) / float64(analytics.TotalTickets) * 100
	}

	// Get average resolution time
	var avgResolutionMinutes float64
	r.data.db.WithContext(ctx).Model(&entity.ServiceTicket{}).
		Where("created_at BETWEEN ? AND ? AND actual_duration_minutes IS NOT NULL", filters.DateFrom, filters.DateTo).
		Select("AVG(actual_duration_minutes)").
		Scan(&avgResolutionMinutes)
	analytics.AverageResolutionTimeHours = avgResolutionMinutes / 60.0

	// Get total revenue
	r.data.db.WithContext(ctx).Model(&entity.ServiceTicket{}).
		Where("created_at BETWEEN ? AND ?", filters.DateFrom, filters.DateTo).
		Select("COALESCE(SUM(actual_cost), 0)").
		Scan(&analytics.TotalRevenue)

	if analytics.TotalTickets > 0 {
		analytics.AverageTicketValue = analytics.TotalRevenue / float64(analytics.TotalTickets)
	}

	// Calculate SLA compliance
	var slaCompliantTickets int64
	r.data.db.WithContext(ctx).Model(&entity.ServiceTicket{}).
		Where("created_at BETWEEN ? AND ? AND sla_breached = ?", filters.DateFrom, filters.DateTo, false).
		Count(&slaCompliantTickets)

	if analytics.TotalTickets > 0 {
		analytics.SLAComplianceRate = float64(slaCompliantTickets) / float64(analytics.TotalTickets) * 100
	}

	return analytics, nil
}

// GetTechnicianPerformance retrieves technician performance metrics
func (r *serviceTicketRepo) GetTechnicianPerformance(ctx context.Context, technicianID int64, dateFrom, dateTo time.Time) (*biz.TechnicianPerformance, error) {
	r.log.WithContext(ctx).Infof("Getting technician performance from database: %d", technicianID)

	performance := &biz.TechnicianPerformance{
		TechnicianID:   technicianID,
		TechnicianName: "Technician", // Would be populated from user service
	}

	query := r.data.db.WithContext(ctx).Model(&entity.ServiceTicket{}).
		Where("assigned_technician_id = ? AND created_at BETWEEN ? AND ?", technicianID, dateFrom, dateTo)

	// Get basic counts
	var ticketsAssigned int64
	query.Count(&ticketsAssigned)
	performance.TicketsAssigned = int32(ticketsAssigned)

	var ticketsCompleted int64
	r.data.db.WithContext(ctx).Model(&entity.ServiceTicket{}).
		Where("assigned_technician_id = ? AND created_at BETWEEN ? AND ? AND status IN ?",
			technicianID, dateFrom, dateTo,
			[]entity.TicketStatus{entity.TicketStatusCompleted, entity.TicketStatusClosed}).
		Count(&ticketsCompleted)
	performance.TicketsCompleted = int32(ticketsCompleted)

	var ticketsOverdue int64
	r.data.db.WithContext(ctx).Model(&entity.ServiceTicket{}).
		Where("assigned_technician_id = ? AND created_at BETWEEN ? AND ? AND due_date < ? AND status NOT IN ?",
			technicianID, dateFrom, dateTo, time.Now(),
			[]entity.TicketStatus{entity.TicketStatusCompleted, entity.TicketStatusClosed}).
		Count(&ticketsOverdue)
	performance.TicketsOverdue = int32(ticketsOverdue)

	// Calculate completion rate
	if performance.TicketsAssigned > 0 {
		performance.CompletionRate = float64(performance.TicketsCompleted) / float64(performance.TicketsAssigned) * 100
	}

	// Get average resolution time
	var avgResolutionMinutes float64
	r.data.db.WithContext(ctx).Model(&entity.ServiceTicket{}).
		Where("assigned_technician_id = ? AND created_at BETWEEN ? AND ? AND actual_duration_minutes IS NOT NULL",
			technicianID, dateFrom, dateTo).
		Select("AVG(actual_duration_minutes)").
		Scan(&avgResolutionMinutes)
	performance.AverageResolutionTimeHours = avgResolutionMinutes / 60.0

	// Get average customer rating
	r.data.db.WithContext(ctx).Model(&entity.ServiceTicket{}).
		Where("assigned_technician_id = ? AND created_at BETWEEN ? AND ? AND customer_rating IS NOT NULL",
			technicianID, dateFrom, dateTo).
		Select("AVG(customer_rating)").
		Scan(&performance.CustomerRatingAverage)

	// Get revenue generated
	r.data.db.WithContext(ctx).Model(&entity.ServiceTicket{}).
		Where("assigned_technician_id = ? AND created_at BETWEEN ? AND ?", technicianID, dateFrom, dateTo).
		Select("COALESCE(SUM(actual_cost), 0)").
		Scan(&performance.RevenueGenerated)

	// Calculate efficiency score (simplified)
	performance.EfficiencyScore = (performance.CompletionRate + performance.CustomerRatingAverage*20) / 2

	// Add sample strengths and improvement areas
	if performance.CustomerRatingAverage >= 4.0 {
		performance.Strengths = append(performance.Strengths, "Excellent customer satisfaction")
	}
	if performance.CompletionRate >= 90.0 {
		performance.Strengths = append(performance.Strengths, "High completion rate")
	}
	if performance.TicketsOverdue > 0 {
		performance.ImprovementAreas = append(performance.ImprovementAreas, "Reduce overdue tickets")
	}

	return performance, nil
}

// GetSLAReport retrieves SLA compliance report
func (r *serviceTicketRepo) GetSLAReport(ctx context.Context, dateFrom, dateTo time.Time, priority *entity.TicketPriority) (*biz.SLAReport, error) {
	r.log.WithContext(ctx).Info("Getting SLA report from database")

	report := &biz.SLAReport{}

	query := r.data.db.WithContext(ctx).Model(&entity.ServiceTicket{}).
		Where("created_at BETWEEN ? AND ?", dateFrom, dateTo)

	if priority != nil {
		query = query.Where("priority = ?", *priority)
	}

	// Get basic counts
	var totalTickets int64
	query.Count(&totalTickets)
	report.TotalTickets = int32(totalTickets)

	var slaMet int64
	query.Where("sla_breached = ?", false).Count(&slaMet)
	report.SLAMet = int32(slaMet)

	report.SLABreached = report.TotalTickets - report.SLAMet

	if report.TotalTickets > 0 {
		report.SLAComplianceRate = float64(report.SLAMet) / float64(report.TotalTickets) * 100
	}

	return report, nil
}

// RateService adds customer rating to a ticket
func (r *serviceTicketRepo) RateService(ctx context.Context, ticketID int64, rating int32, feedback string, customerID int64) error {
	r.log.WithContext(ctx).Infof("Rating service for ticket: %d", ticketID)

	var ticket entity.ServiceTicket
	err := r.data.db.WithContext(ctx).
		Where("id = ? AND customer_id = ?", ticketID, customerID).
		First(&ticket).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return biz.ErrTicketNotFound
		}
		return err
	}

	// Update rating
	ticket.CustomerRating = &rating
	ticket.CustomerFeedback = feedback
	ticket.UpdatedAt = time.Now()

	if err := r.data.db.WithContext(ctx).Save(&ticket).Error; err != nil {
		r.log.WithContext(ctx).Errorf("Failed to rate service: %v", err)
		return err
	}

	return nil
}

// GetCustomerTickets retrieves tickets for a customer with summary
func (r *serviceTicketRepo) GetCustomerTickets(ctx context.Context, customerID int64, filters biz.CustomerTicketFilters) ([]*entity.ServiceTicket, int32, *biz.CustomerTicketSummary, error) {
	r.log.WithContext(ctx).Infof("Getting customer tickets from database: %d", customerID)

	var tickets []*entity.ServiceTicket
	var total int64

	query := r.data.db.WithContext(ctx).
		Model(&entity.ServiceTicket{}).
		Where("customer_id = ?", customerID)

	// Apply filters
	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}
	if filters.DateFrom != nil {
		query = query.Where("created_at >= ?", *filters.DateFrom)
	}
	if filters.DateTo != nil {
		query = query.Where("created_at <= ?", *filters.DateTo)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		r.log.WithContext(ctx).Errorf("Failed to count customer tickets: %v", err)
		return nil, 0, nil, err
	}

	// Apply pagination and get results
	offset := (filters.Page - 1) * filters.PageSize
	err := query.
		Offset(int(offset)).
		Limit(int(filters.PageSize)).
		Order("created_at DESC").
		Find(&tickets).Error

	if err != nil {
		r.log.WithContext(ctx).Errorf("Failed to get customer tickets: %v", err)
		return nil, 0, nil, err
	}

	// Create summary
	summary := &biz.CustomerTicketSummary{
		CustomerID: customerID,
	}

	// Get summary statistics
	r.data.db.WithContext(ctx).Model(&entity.ServiceTicket{}).
		Where("customer_id = ?", customerID).
		Count(&total)
	summary.TotalTickets = int32(total)

	var openTickets int64
	r.data.db.WithContext(ctx).Model(&entity.ServiceTicket{}).
		Where("customer_id = ? AND status NOT IN ?", customerID,
			[]entity.TicketStatus{entity.TicketStatusCompleted, entity.TicketStatusClosed, entity.TicketStatusCancelled}).
		Count(&openTickets)
	summary.OpenTickets = int32(openTickets)

	var completedTickets int64
	r.data.db.WithContext(ctx).Model(&entity.ServiceTicket{}).
		Where("customer_id = ? AND status IN ?", customerID,
			[]entity.TicketStatus{entity.TicketStatusCompleted, entity.TicketStatusClosed}).
		Count(&completedTickets)
	summary.CompletedTickets = int32(completedTickets)

	// Get average rating
	r.data.db.WithContext(ctx).Model(&entity.ServiceTicket{}).
		Where("customer_id = ? AND customer_rating IS NOT NULL", customerID).
		Select("AVG(customer_rating)").
		Scan(&summary.AverageRating)

	// Get total spent
	r.data.db.WithContext(ctx).Model(&entity.ServiceTicket{}).
		Where("customer_id = ?", customerID).
		Select("COALESCE(SUM(actual_cost), 0)").
		Scan(&summary.TotalSpent)

	return tickets, int32(total), summary, nil
}
